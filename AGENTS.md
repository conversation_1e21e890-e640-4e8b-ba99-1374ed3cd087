# Repository Guidelines

## Project Structure & Module Organization
- `src/main/java/com/ebon/energy/fms`: Spring Boot app (controllers, services, mappers, config, util, domain).
- `src/main/resources`: app configuration (`application.yaml`, `application-*.yaml`), MyBatis mappers.
- `src/test/java`: JUnit 5 tests (e.g., `InverterTest.java`).
- `target/fms-server.jar`: build artifact; used by `Dockerfile`.

## Build, Test, and Development Commands
- Build: `mvn clean package -DskipTests`
  - Produces `target/fms-server.jar`.
- Test: `mvn test`
  - Runs JUnit 5/Spring tests.
- Run (local): `mvn spring-boot:run -Dspring-boot.run.profiles=local`
  - Uses `src/main/resources/application-local.yaml` (default port 8080).
- Run (jar): `java -jar target/fms-server.jar --spring.profiles.active=test`
- Format (IDE): apply Java 11 formatting before committing.

## Coding Style & Naming Conventions
- Java 11, 4‑space indentation, UTF‑8.
- Packages: `com.ebon.energy.fms.*` (e.g., `controller`, `service`, `mapper`, `domain.{entity,vo,po}`).
- Classes: `PascalCase`; methods/fields: `camelCase`.
- REST: controllers end with `Controller`, services with `Service`, mappers with `Mapper`.
- Use Lombok where present, MapStruct for mappings; avoid business logic in controllers.

## Testing Guidelines
- Frameworks: JUnit 5, Spring Boot Test.
- Test names: mirror class names, suffix `Test` (e.g., `InverterServiceTest`).
- Scope: cover service logic and repository queries; prefer `@DataJpaTest`/slice tests when possible.
- Run locally with representative Spring profile and test data.

## Commit & Pull Request Guidelines
- Commits: concise imperative subject, scoped module prefix when helpful (e.g., `service: add inverter status cache`).
- PRs: clear description, rationale, screenshots/logs for API changes, linked issues, and steps to validate.
- Requirements: builds green, tests pass, no console errors, no secrets in diffs.

## Security & Configuration Tips
- Profiles: manage env via `application-*.yaml`; do not hard‑code credentials.
- Secrets: use environment variables or an external vault; exclude from VCS.
- Databases: multiple data sources are configured (`Primary/Second/Third/FourthDataSourceConfig`); verify local access before running queries.
