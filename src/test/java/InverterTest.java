import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ebon.energy.fms.FmsServerApplication;
import com.ebon.energy.fms.common.exception.BizException;
import com.ebon.energy.fms.domain.entity.InvertersDO;
import com.ebon.energy.fms.domain.po.SiteListPO;
import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.site.SiteHistoryVO;
import com.ebon.energy.fms.domain.vo.telemetry.DataWithLinks;
import com.ebon.energy.fms.domain.vo.telemetry.InverterInfo;
import com.ebon.energy.fms.mapper.second.InvertersMapper;
import com.ebon.energy.fms.service.InverterService;
import com.ebon.energy.fms.service.SiteDashboardHistoryService;
import com.ebon.energy.fms.service.SiteService;
import com.ebon.energy.fms.service.TelemetryService;
import com.ebon.energy.fms.util.HttpClientUtil;
import com.ebon.energy.fms.util.JsonComparison;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@Slf4j
@SpringBootTest(classes = FmsServerApplication.class)
public class InverterTest {

    @Resource
    private InvertersMapper invertersMapper;
    
    @Resource
    private SiteService siteService;

    @Resource
    private InverterService inverterService;

    @Resource
    private TelemetryService telemetryService;
    
    @Resource
    private SiteDashboardHistoryService siteDashboardHistoryService;

    @Test
    public void testInverter() {
        List<InvertersDO> list = invertersMapper.selectList(new QueryWrapper<>());
        int i = 0;
        List<String> errors = new ArrayList<>();
        for (InvertersDO invertersDO : list) {
            if (i >= 1000) {
                break;
            }

            try {
                System.out.println((i++) + " sn:" + invertersDO.getSerialNumber());
                System.out.println(inverterService.getInverterInfo(invertersDO.getSerialNumber()));
            } catch (BizException e) {
                System.out.println("biz errorMsg:" + invertersDO.getSerialNumber() + " " + e.getErrorMsg());
            } catch (Exception e) {
                errors.add(invertersDO.getSerialNumber());
                System.out.println("testInverter error: " + invertersDO.getSerialNumber());
                e.printStackTrace();
            }
        }
        log.error("errors:{}", errors);
    }
    
    @Test
    public void testLastTelemetry() {
        testLastTelemetry(1);
    }

    public void testLastTelemetry(int p) {
        Page<InvertersDO> page = new Page<>(p, 500);
        LambdaQueryWrapper<InvertersDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(InvertersDO::getSerialNumber, Lists.newArrayList("RB22050508110048","RB22060705110368",  "RB22060705110382" ,"RB21120804110018","RB22062802400008","RB22070502400013"));
        List<InvertersDO> list = invertersMapper.selectPage(page, queryWrapper).getRecords();
        int i = 0;
        List<String> errors = new ArrayList<>();
        for (InvertersDO invertersDO : list) {
            try {
                System.out.println((i++) + " sn:" + invertersDO.getSerialNumber());
                DataWithLinks<InverterInfo> lastTelemetry = telemetryService.getLastTelemetry(invertersDO.getSerialNumber());

                String portalRes = getPortalTelemetryRes(invertersDO.getSerialNumber());
                ObjectMapper objectMapper = new ObjectMapper();
                objectMapper.registerModule(new JavaTimeModule());
                JsonComparison.compareJsonStrings(objectMapper.writeValueAsString(lastTelemetry), portalRes);
            } catch (BizException e) {
                System.out.println("biz errorMsg:" + invertersDO.getSerialNumber() + " " + e.getErrorMsg());
            } catch (Exception e) {
                errors.add(invertersDO.getSerialNumber());
                System.out.println("testLastTelemetry error: " + invertersDO.getSerialNumber());
                e.printStackTrace();
            }
        }
        log.error("errors:{}", errors);
    }

    @Test
    public void testLastStatus() {
        Page<InvertersDO> page = new Page<>(1, 500);
        LambdaQueryWrapper<InvertersDO> queryWrapper = new LambdaQueryWrapper<>();
        List<InvertersDO> list = invertersMapper.selectPage(page, queryWrapper).getRecords();
        int i = 0;
        List<String> errors = new ArrayList<>();
        for (InvertersDO invertersDO : list) {
            try {
                System.out.println((i++) + " sn:" + invertersDO.getSerialNumber());
                DataWithLinks<InverterInfo> lastStatus = telemetryService.getLastStatus(invertersDO.getSerialNumber(), false);

                String portalRes = getPortalSystemStatusRes(invertersDO.getSerialNumber());
                ObjectMapper objectMapper = new ObjectMapper();
                objectMapper.registerModule(new JavaTimeModule());
                JsonComparison.compareJsonStrings(objectMapper.writeValueAsString(lastStatus), portalRes);
            } catch (BizException e) {
                System.out.println("biz errorMsg:" + invertersDO.getSerialNumber() + " " + e.getErrorMsg());
            } catch (Exception e) {
                errors.add(invertersDO.getSerialNumber());
                System.out.println("testLastStatus error: " + invertersDO.getSerialNumber());
                e.printStackTrace();
            }
        }
        log.error("errors:{}", errors);
    }

    @Test
    public void testLastEnergyflow() {
        Page<InvertersDO> page = new Page<>(1, 1000);
        LambdaQueryWrapper<InvertersDO> queryWrapper = new LambdaQueryWrapper<>();
        List<InvertersDO> list = invertersMapper.selectPage(page, queryWrapper).getRecords();
        int i = 0;
        List<String> errors = new ArrayList<>();
        for (InvertersDO invertersDO : list) {
            try {
                System.out.println((i++) + " sn:" + invertersDO.getSerialNumber());
                DataWithPermalinkVO<EnergyFlowExtendedVO> lastEnergyflow = telemetryService.getLastEnergyflow(invertersDO.getSerialNumber(), null);

                String portalRes = getPortalEnergyflowRes(invertersDO.getSerialNumber());
                ObjectMapper objectMapper = new ObjectMapper();
                objectMapper.registerModule(new JavaTimeModule());
                JsonComparison.compareJsonStrings(objectMapper.writeValueAsString(lastEnergyflow), portalRes);
            } catch (BizException e) {
                System.out.println("biz errorMsg: " + invertersDO.getSerialNumber() + " " + e.getErrorMsg());
            } catch (Exception e) {
                errors.add(invertersDO.getSerialNumber());
                System.out.println("testLastEnergyflow error: " + invertersDO.getSerialNumber());
                e.printStackTrace();
            }
        }
        log.error("errors:{}", errors);
    }

    @Test
    public void testSiteHistory() {
        SiteListPO siteListPO = new SiteListPO();
        siteListPO.setCurrent(1);
        siteListPO.setPageSize(1500);
        PageResult<SiteVO> siteList = siteService.getSiteList(siteListPO);
        int i = 0;
        List<String> errors = new ArrayList<>();
        for (SiteVO siteVO : siteList.getList()) {
            try {
                System.out.println((i++) + " siteId:" + siteVO.getSiteId());
                SiteHistoryVO siteHistory = siteDashboardHistoryService.getSiteHistory(siteVO.getSiteId(), 7);

                String portalRes = getPortalSiteHistorRes(siteVO.getSiteId());
                ObjectMapper objectMapper = new ObjectMapper();
                objectMapper.registerModule(new JavaTimeModule());
                JsonComparison.compareJsonStrings(objectMapper.writeValueAsString(siteHistory), portalRes);
            } catch (BizException e) {
                System.out.println("biz errorMsg: " + siteVO.getSiteId() + " " + e.getErrorMsg());
            } catch (Exception e) {
                errors.add(siteVO.getSiteId());
                System.out.println("testSiteHistory error: " + siteVO.getSiteId());
                e.printStackTrace();
            }
        }
        log.error("errors:{}", errors);
    }

    @Test
    public void testBatch() throws ExecutionException, InterruptedException {

        CompletableFuture future1 = CompletableFuture.supplyAsync(() -> {
            testLastTelemetry(5);
            return null;
        });
        
        CompletableFuture future2 = CompletableFuture.supplyAsync(() -> {
            testLastTelemetry(15);
            return null;
        });

        CompletableFuture future3 = CompletableFuture.supplyAsync(() -> {
            testLastTelemetry(19);
            return null;
        });

        CompletableFuture future4 = CompletableFuture.supplyAsync(() -> {
            testLastTelemetry(22);
            return null;
        });
        
        future1.get();
        future2.get();
        future3.get();
        future4.get();
    }

    private String getPortalEnergyflowRes(String sn) {
        String url = "https://rbtestwebui-leia.azurewebsites.net/api/v2/energyflowd2/";
        String cookie = "ai_user=EVnFe|2025-03-17T01:21:22.872Z; .AspNet.ApplicationCookie=29vjFjaQuycGIrQxPmvMepgNN6rSP9B9TLS9ZnYrl__b6_1FQ0u3zQA6S5W9lE1Saj6NGQZpBd94SNxpMJYALeLGrQC6LTy1VylJstyN2xwwEOZpwlh7p-YU9yhkC2NpKZIrfWwEmeZVbuGnvrgnhfilAFdQhzsyYmbZiVfTUxfqaLGE78cz3qVgrFSBvexcYZ4Ezk9qsVltrZbG73fn_kpvOK_0eVVxNmQsaHZKxS8b0m6hOfQmAY4TCYewIxKS7BD0h0qHieCf4lCplfqO2OQwqnf-O9QmYneQTp0RqlPWyCVI82rI_MiLkJzhkVR4Z7iqAWtm86iXTvOljxf1Ncyfr4duutZOq_DBdkyWKjJ0w2lBFyccXbE8azEWehTpU-ZTSrblK75wBPHviG6knBV8jeFe8QVV2J88vYvk3fyP9Zxc46oTXEbU6Klje7TBVW8x6DgD1UbzTZsfbNo_PtVd9iMeGSQ2SI_ikhRRrOmUmNwvmkQLglPORivpuehP6ijqKt_QZpB7CMH82CfGGOG7jNI9-f-6Q9XWYO3dyJC9NPS5MgNbheEhdet4LbP_KwGDANyrsWm4K3neh-sWew; ARRAffinity=6d3d50ebcd0e89ffba781af0478dd8f0a095e9e6d896b3eb9a6ff4d70015a1fd; ARRAffinitySameSite=6d3d50ebcd0e89ffba781af0478dd8f0a095e9e6d896b3eb9a6ff4d70015a1fd; __RequestVerificationToken=8h1zNX8UzF8_57w17y-GWAtodaU55_v7Sx_bjyoCAdppbsyhyDu0QuADxtqZ7XPCnp76G7XGFP7XAhniXF-VN8flWYfNicwWTgaD-HI2PYU1; ai_session=7qtwf5bwazk/dwGmQ7OdO+|1745911398249|1745927488925";
        //String url = "https://portal.redbacktech.com/api/v2/energyflowd2/";
        //String cookie = "xxx";
        return HttpClientUtil.get(url + sn, cookie);
    }

    private String getPortalSystemStatusRes(String sn) {
        String url = "https://rbtestwebui-leia.azurewebsites.net/product/"+sn+"/data/status/latest";
        String cookie = "ai_user=EVnFe|2025-03-17T01:21:22.872Z; .AspNet.ApplicationCookie=29vjFjaQuycGIrQxPmvMepgNN6rSP9B9TLS9ZnYrl__b6_1FQ0u3zQA6S5W9lE1Saj6NGQZpBd94SNxpMJYALeLGrQC6LTy1VylJstyN2xwwEOZpwlh7p-YU9yhkC2NpKZIrfWwEmeZVbuGnvrgnhfilAFdQhzsyYmbZiVfTUxfqaLGE78cz3qVgrFSBvexcYZ4Ezk9qsVltrZbG73fn_kpvOK_0eVVxNmQsaHZKxS8b0m6hOfQmAY4TCYewIxKS7BD0h0qHieCf4lCplfqO2OQwqnf-O9QmYneQTp0RqlPWyCVI82rI_MiLkJzhkVR4Z7iqAWtm86iXTvOljxf1Ncyfr4duutZOq_DBdkyWKjJ0w2lBFyccXbE8azEWehTpU-ZTSrblK75wBPHviG6knBV8jeFe8QVV2J88vYvk3fyP9Zxc46oTXEbU6Klje7TBVW8x6DgD1UbzTZsfbNo_PtVd9iMeGSQ2SI_ikhRRrOmUmNwvmkQLglPORivpuehP6ijqKt_QZpB7CMH82CfGGOG7jNI9-f-6Q9XWYO3dyJC9NPS5MgNbheEhdet4LbP_KwGDANyrsWm4K3neh-sWew; ARRAffinity=6d3d50ebcd0e89ffba781af0478dd8f0a095e9e6d896b3eb9a6ff4d70015a1fd; ARRAffinitySameSite=6d3d50ebcd0e89ffba781af0478dd8f0a095e9e6d896b3eb9a6ff4d70015a1fd; __RequestVerificationToken=8h1zNX8UzF8_57w17y-GWAtodaU55_v7Sx_bjyoCAdppbsyhyDu0QuADxtqZ7XPCnp76G7XGFP7XAhniXF-VN8flWYfNicwWTgaD-HI2PYU1; ai_session=7qtwf5bwazk/dwGmQ7OdO+|1745911398249|1745927488925";
        //String url = "https://portal.redbacktech.com/product/" + sn + "/data/status/latest";
        //String cookie = "xxx";
        return HttpClientUtil.get(url, cookie);
    }

    private String getPortalTelemetryRes(String sn) {
        String url = "https://rbtestwebui-leia.azurewebsites.net/product/"+sn+"/data/telemetry/latest";
        String cookie = "ai_user=EVnFe|2025-03-17T01:21:22.872Z; .AspNet.ApplicationCookie=zdQfNWEPNde-RTrqIA0eN44JpKsQqBdnLauNOu6dvExLg8TVVNImgDu3nO2T5c55RJpUiSv1sPesnRdAaIR5X-3ztwM6ZXOviCkW_HmasLhL5FId571OtRxcIfLgzGm70X-I7s6_wNFibRdH2gPpIZ0oUEIzdCNXqRIL3JWFozEzn8E5wUbchTfguPxkXLZA-iiN9pSi8VigGAWUpUOHqPjBFrCKVkctwQbO1coa_uAvAlzKTKBXpW-Ctd6aQNcQTOkt7bJASoFnXzl0wJSkl0IXjtuv1n4kJZuIH83r3zoTbUU30lPlg8uupZrXyIefFwsWcdEBPOysYmZDVo77jBSjGBgBb5s7y_7C_La841qFG8XjQbE5I4_jkAGtW-q9bGjDlF8EwQGRxBVIxPygoYry7ERomAN5AjXNp8cfzTV69P4jkNvkSXSW26kicftap5uZYc01Qo8OR8yJEzIxaSPAfUPkiuwTEIHCr5Zp32Li-GDBzxqdnjbthPUCRaFmN9Piw3nAYTjw93o065y62p_8fqt7m2XN1k9ACKEah0oGj8o8Zitjeg4bNExlyuX-BR2qmSKNHuNOrtTcIYAgeg; ARRAffinity=a5b9f4b6d50bfa11e84342298fd5cd54ff5d176198c38c2d5f2e6030470fa881; ARRAffinitySameSite=a5b9f4b6d50bfa11e84342298fd5cd54ff5d176198c38c2d5f2e6030470fa881; __RequestVerificationToken=2xCudlyOwDZP6Sl2uwfXoW6eTnB6kw6omIN_k-efUvdlPL1D1pvMWdHuAF02OeXsnQ75658iriMy2YYnFfBWh2tYATn5Vc8OaYQ5gkOa3sw1; ai_session=8dfUH|1750656241338|1750734199345";
        //String url = "https://portal.redbacktech.com/product/" + sn + "/data/telemetry/latest";
        //String cookie = "xxx";
        return HttpClientUtil.get(url, cookie);
    }

    private String getPortalSiteHistorRes(String siteId) {
        String url = "https://rbtestwebui-leia.azurewebsites.net/api/v2/portal/site/"+siteId+"/History";
        String cookie = "ai_user=EVnFe|2025-03-17T01:21:22.872Z; .AspNet.ApplicationCookie=7iP-EJ5bR2IetvQDkJjRbFJT12eQSMlOiA33m6TDErHSFBdLMHIJVY3TKkq6vw-i_z5BLG2xtH_OBe44VwU-ODje1Q8oL3S66svisrnVQEn2jvpAZZyj1-6j1LzQFlqWQCb78B5-ONGbsr6KNcrPlBFRA483MRwWTDnePyCZZ2zkPv0Z2NXv7MQdB7qwd3C3od-Or7waZlPdFS_Hd3Zk79goC6OqNh2cIzeBZTRJMB0sGTaRR4D1XikArswSi31GCdCN3Ao8H4S5Fy4ljH5OEQQbqeP-lDHGBn6-ubmD-zVceUOaG_zZ92yfJfNg1iiE7oST8PViBO2SOoDxgkRddu4-QjGjzXm3v5i1HVVb2YeceWLQ1xjJ6HsbSgBGcExDMCO8NBbC-FLbQW4hf67Dh19FDK-0df6BGs5Qc5aIGuljWj2jZ1kwmzr27yT2NLDuCLlmtHaF_-2mX6zTW-pV5JTt2GKwuqabS0Mhi6PL95_w52tv8o2-e23Ifb_0v__ipBxpckcIzpHd4d0O6fWd0VvY9oOWrK274vNdqtZqhBeBhcCUjt0ILaTYj_2YSTJG; ARRAffinity=9e20f0a55da29eeaecf16d187f70be42d681bac6555a1167ca962e98272252aa; ARRAffinitySameSite=9e20f0a55da29eeaecf16d187f70be42d681bac6555a1167ca962e98272252aa; __RequestVerificationToken=GxrYPKCwceH3AOlTWhQzktCUrEWhgn0nbQV4CDyO4OM8zKWwOrM2AW9kxxU1WCzY7nXGmw5006fEqflB0pxo9MYVSvNYkM2PM4wao2ZNr9I1; ai_session=4NNpOOlMgWN0piXvfG0OfI|1749020387943|1749091984707";
        //String url = "https://portal.redbacktech.com/api/v2/portal/site/"+siteId+"/History";
        //String cookie = "xxx";
        return HttpClientUtil.get(url, cookie);
    }
    
}
