package com.ebon.energy.fms.mapper.third;

import com.ebon.energy.fms.domain.vo.product.control.InternalModelInfo;
import org.apache.ibatis.annotations.*;

/**
 * 规格相关的数据库映射接口
 */
@Mapper
public interface SpecificationMapper {

    /**
     * 获取模型信息
     * 基于C#代码的SQL查询逻辑
     * 
     * @param contextUserId 上下文用户ID
     * @param serialNumber 序列号
     * @return 内部模型信息
     */
    @Select({
        "SELECT ",
        "  JSON_VALUE(product.LatestSystemStatus, '$.Inverter.ModelName') as modelName,",
        "  JSON_VALUE(product.LatestSystemStatus, '$.Inverter.FirmwareVersion') as firmwareVersion,",
        "  settings.DesiredDeviceSettings as desired,",
        "  settings.ReportedDeviceSettings as reported",
        "FROM RedbackProducts product ",
        "-- LEFT JOINS because ROSS1 AND ROSS2",
        "LEFT JOIN Device device on device.SerialNumber = product.RedbackProductSn AND device.ApplicationName = 'Ross'",
        "LEFT JOIN DeviceSettings settings on settings.DeviceId = device.Id",
        "WHERE RedbackProductSn = #{serialNumber}"
    })
    InternalModelInfo getModelInfo(
        @Param("contextUserId") String contextUserId,
        @Param("serialNumber") String serialNumber
    );
}
