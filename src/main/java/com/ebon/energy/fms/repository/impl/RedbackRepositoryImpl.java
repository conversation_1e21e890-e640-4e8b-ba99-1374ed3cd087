package com.ebon.energy.fms.repository.impl;

import com.alibaba.fastjson.JSONObject;
import com.ebon.energy.fms.common.enums.ConfigurationType;
import com.ebon.energy.fms.common.enums.FirmwareVersion;
import com.ebon.energy.fms.common.exception.BizException;
import com.ebon.energy.fms.common.utils.ACCoupledUtility;
import com.ebon.energy.fms.common.utils.WASafetyCountryUtility;
import com.ebon.energy.fms.domain.entity.*;
import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.product.control.DeviceControlDTO;
import com.ebon.energy.fms.domain.vo.product.control.ProductDTO;
import com.ebon.energy.fms.domain.vo.product.control.ProductInfo;
import com.ebon.energy.fms.domain.vo.product.control.RossVersion;
import com.ebon.energy.fms.domain.vo.product.control.UpdateDeviceControlDTO;
import com.ebon.energy.fms.domain.vo.setting.UpdateElectricalConfigurationDTO;
import com.ebon.energy.fms.domain.vo.telemetry.SystemStatus;
import com.ebon.energy.fms.mapper.third.ConfigurationAuditMapper;
import com.ebon.energy.fms.mapper.third.ConfigurationsMapper;
import com.ebon.energy.fms.mapper.third.GroupEventMapper;
import com.ebon.energy.fms.mapper.third.ProductControlMapper;
import com.ebon.energy.fms.mapper.third.ProductGroupComplianceMapper;
import com.ebon.energy.fms.mapper.third.ProductMapper;
import com.ebon.energy.fms.repository.RedbackRepository;

import com.ebon.energy.fms.util.RequestUtil;
import com.ebon.energy.fms.util.SystemStatusHelper;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;

import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.util.Date;

/**
 * Implementation of the RedbackRepository interface
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class RedbackRepositoryImpl implements RedbackRepository {

    private final ObjectMapper objectMapper;

    private final ProductMapper productMapper;

    private final ProductControlMapper productControlMapper;
    private final ConfigurationsMapper configurationsMapper;
    private final ConfigurationAuditMapper configurationAuditMapper;
    private final GroupEventMapper groupEventMapper;
    private final ProductGroupComplianceMapper productGroupComplianceMapper;

    /**
     * Convert system status JSON to SystemStatus object
     *
     * @param systemStatusJson The system status JSON
     * @return The SystemStatus object
     */
    private SystemStatus convertSystemStatus(String systemStatusJson) {
        try {
            return objectMapper.readValue(systemStatusJson, SystemStatus.class);
        } catch (Exception e) {
            log.error("Error converting system status JSON: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * Get product information as a ProductDTO
     *
     * @param serialNumber Product serial number
     * @return ProductDTO containing product information
     */
    @Override
    public ProductDTO getProductDTO(String serialNumber) {

        try {
            log.info("Getting product DTO  and serial number {}", serialNumber);

            // Get product information
            ProductInfo product = productMapper.getProductInfo(serialNumber);
            if (product == null) {
                throw new RuntimeException("The serial number doesn't exist.");
            }
            // Parse system status if available
            SystemStatus systemStatus = null;

            if (product.getLatestSystemStatus() != null && !product.getLatestSystemStatus().isEmpty()) {
                // Convert the JSON string to SystemStatusDto object
                // log.info("JSON:{}", product.getLatestSystemStatus());
                systemStatus = JSONObject.parseObject(product.getLatestSystemStatus(), SystemStatus.class);
            }

            // Create and return the ProductDTO
            ProductDTO productDTO = new ProductDTO(product, systemStatus);

            log.info("Successfully retrieved product DTO for serial number: {}", serialNumber);
            return productDTO;
        } catch (Exception e) {
            log.error("Error getting product DTO  and serial number {}: {}", serialNumber, e.getMessage(), e);
            throw new RuntimeException("Error getting product information: " + e.getMessage(), e);
        }
    }

    @Override
    public com.ebon.energy.fms.domain.vo.product.control.DeviceControlDTO getDeviceControl(String serialNumber) {
        try {
            ProductInfo product = productMapper.getProductInfo(serialNumber);
            if (product == null) {
                throw new RuntimeException("The serial number doesn't exist.");
            }
            String cloudConfigJson = product.getCloudConfiguration();
            DeviceControl cloudConfig = objectMapper.readValue(cloudConfigJson, DeviceControl.class);
            if (cloudConfig != null) {
                if (cloudConfig.getDateTimeUpdated() == null) {
                    SimpleDateFormat sdf = new SimpleDateFormat(DeviceControl.LOCAL_DATE_TIME_FORMAT);
                    cloudConfig.setDateTimeUpdated(sdf.format(new Date()));
                }

                cloudConfig.setDeviceSerialNumber(serialNumber);
            }
            DeviceControlDTO dto = new DeviceControlDTO();
            dto.setDeviceControl(cloudConfig);
            dto.setInverterTimeZoneId(product.getInverterTimeZone());
            return dto;
        } catch (Exception e) {
            log.error("Error getting device control serial number {}: {}", serialNumber, e.getMessage(), e);
            throw new RuntimeException("Error retrieving device control data", e);
        }
    }

    @Override
    public void updateDeviceControlRoss1(UpdateDeviceControlDTO updateDeviceControlDTO) {
        var product = productControlMapper.getProductBySerial(updateDeviceControlDTO.getSerialNumber());
        if (product == null) {
            throw new BizException("The serial number doesn't exist.");
        }
        validateConfigurationOrThrow(updateDeviceControlDTO);

        var isOptimised = product.canBeOptimised() && product.getIsOptimised();

        var isFromWebJob = false;

        var isFromDevice = false;
        if (isOptimised) {
            var shouldDisableOptimisation = updateDeviceControlDTO.isFromPortal();
            var shouldOverrideOptimisation = isFromWebJob || isFromDevice;
            var shouldAcceptDeviceControl = shouldDisableOptimisation || shouldOverrideOptimisation;
            if (!shouldAcceptDeviceControl) {
                // var errorMessage = isOwner
                // ? "Please disable optimisation through the portal before updating the device
                // control."
                // : "You don't have access to disable optimisation";
                // throw new UnauthorizedAccessException(errorMessage);
            }
            if (shouldDisableOptimisation) {
                // // must be a home owner to disable optimisation
                // if (!isOwner)
                // {
                // throw new UnauthorizedAccessException("You don't have access to disable
                // optimisation");
                // }
                productControlMapper.updateProductOptimizationStatus(updateDeviceControlDTO.getSerialNumber(), false);
            }
        }

        if (!isFromDevice && !isFromWebJob) {
            // must have RW on Device Control to udpate device control (unless you are
            // consumer or web job)
            // if (!hasRWDeviceControlAccess)
            // {
            // throw new UnauthorizedAccessException("You don't have access to update device
            // control of this system.");
            // }
        }
        handleConfiguration(updateDeviceControlDTO,
                configurationsMapper.findByRedbackProductSn(updateDeviceControlDTO.getSerialNumber()), isFromDevice);
    }

    @Override
    public SystemStatus getLatestSystemStatusAsync(String serialNumber) {
        var productInfo = productMapper.getProductInfo(serialNumber);
        if (productInfo.getLatestSystemStatus() == null) {
            return null;
        } else {
            return JSONObject.parseObject(productInfo.getLatestSystemStatus(), SystemStatus.class);
        }
    }

    @Override
    public ElectricalControllerDataDto getElectricalControllerDataRoss1(String serialNumber) {
        try {
            // 获取电气配置数据
            var electricalConfigurationDto = productMapper.selectElectricalConfiguration(serialNumber);
            if (electricalConfigurationDto == null) {
                throw new RuntimeException("No electrical configuration found for serial number: " + serialNumber);
            }

            // 反序列化云端配置和设备配置
            ElectricalConfiguration cloudConfig = null;
            ElectricalConfiguration deviceConfig = null;

            if (electricalConfigurationDto.getCloud() != null) {
                cloudConfig = objectMapper.readValue(electricalConfigurationDto.getCloud(),
                        ElectricalConfiguration.class);
            }

            if (electricalConfigurationDto.getDevice() != null) {
                deviceConfig = objectMapper.readValue(electricalConfigurationDto.getDevice(),
                        ElectricalConfiguration.class);
            }

            // 获取最新系统状态
            var systemStatus = getLatestSystemStatusAsync(serialNumber);

            // 获取固件版本和ROSS版本
            FirmwareVersion firmwareVersion = FirmwareVersion.UNKNOWN;
            if (systemStatus != null && systemStatus.getInverter() != null
                    && systemStatus.getInverter().getFirmwareVersion() != null) {
                try {
                    firmwareVersion = com.ebon.energy.fms.common.enums.FirmwareVersion.fromVersionString(systemStatus.getInverter().getFirmwareVersion());
                } catch (Exception e) {
                    log.warn("Failed to parse firmware version: {}", systemStatus.getInverter().getFirmwareVersion());
                }
            }

            RossVersion rossVersion = null;
            if (systemStatus != null && systemStatus.getOuijaBoard() != null
                    && systemStatus.getOuijaBoard().getSoftwareVersion() != null) {
                rossVersion = new RossVersion(systemStatus.getOuijaBoard().getSoftwareVersion());
            }else{
                rossVersion = new RossVersion();
            }

            // 判断是否支持AC耦合和WA安全国家
            boolean isACCoupledSupported = ACCoupledUtility.isACCoupledSupported(deviceConfig, firmwareVersion);
            boolean isWASafetyCountrySupported = WASafetyCountryUtility.isWASafetyCountrySupported(firmwareVersion, rossVersion);

            // 修补遗留电池信息
            if (cloudConfig != null) {
                cloudConfig.patchLegacyBatteryInfo();
            }

            // 构建返回对象
            ElectricalControllerDataDto result = new ElectricalControllerDataDto();
            result.setElectricalConfiguration(cloudConfig);
            result.setIsReadOnly(false);
            result.setIsInSync(cloudConfig != null && cloudConfig.equals(deviceConfig));
            result.setCloudTimestamp(cloudConfig != null ? cloudConfig.getDateTimeUpdated() : null);
            result.setDeviceTimestamp(deviceConfig != null ? deviceConfig.getDateTimeUpdated() : null);
            result.setIsACCoupledSupported(isACCoupledSupported);
            result.setIsWASafetyCountrySupported(isWASafetyCountrySupported);
            result.setIsGridOn(SystemStatusHelper.isOnGrid(systemStatus));
            result.setProductModelName(systemStatus != null && systemStatus.getInverter() != null
                    ? systemStatus.getInverter().getModelName()
                    : null);

            return result;
        } catch (Exception e) {
            log.error("Error getting electrical controller data for serial number {}: {}", serialNumber, e.getMessage(),
                    e);
            throw new RuntimeException("Error retrieving electrical controller data", e);
        }
    }

    @Override
    public void updateElectricalSettingRoss1(UpdateElectricalConfigurationDTO updateElectricalConfigurationDto) {
        try {
            log.info("开始更新电气配置，序列号: {}", updateElectricalConfigurationDto.getSerialNumber());

            // 获取当前登录用户ID
            String loggedInUserId = RequestUtil.getPortolUserId();

            ProductElectricalConfigurationVO product = productMapper.selectProductWithElectricalConfiguration(
                updateElectricalConfigurationDto.getSerialNumber(),
                ConfigurationType.Electrical.getValue()
            );

            if (product == null || product.getProductOwnerId() == null) {
                throw new BizException("序列号不存在");
            }

            // 处理配置更新逻辑
            handleElectricalConfiguration(updateElectricalConfigurationDto, product, loggedInUserId);

            log.info("电气配置更新完成，序列号: {}", updateElectricalConfigurationDto.getSerialNumber());

        } catch (BizException e) {
            log.error("更新电气配置业务异常，序列号: {}, 错误: {}",
                updateElectricalConfigurationDto.getSerialNumber(), e.getErrorMsg(), e);
            throw e;
        } catch (Exception e) {
            log.error("更新电气配置系统异常，序列号: {}, 错误: {}",
                updateElectricalConfigurationDto.getSerialNumber(), e.getMessage(), e);
            throw new BizException("更新电气配置失败");
        }
    }


    private void validateConfigurationOrThrow(UpdateDeviceControlDTO updateDeviceControlDTO) {
        var error = updateDeviceControlDTO.getDeviceControl().validate();
        if (StringUtils.hasLength(error)) {
            throw new BizException("Error updating device control: " + error);
        }
    }

    @SneakyThrows
    private void handleConfiguration(UpdateDeviceControlDTO updateDeviceControlDto, ConfigurationsDO configuration,
            Boolean isFromDevice) {
        if (configuration != null) {
            if (isFromDevice) {
                // TODO
            } else {
                updateDeviceControlDto.getDeviceControl().setGroupEventId(null);
                var deviceControl = objectMapper.writeValueAsString(updateDeviceControlDto.getDeviceControl());
                configuration.setConfigurations(deviceControl);
            }
            configuration.setModifiedDateTime(Timestamp.from(Instant.now()));
            configuration.setLastModifiedById(RequestUtil.getLoginUserEmail());
            configurationsMapper.updateById(configuration);
        } else {
            var deviceControl = objectMapper.writeValueAsString(updateDeviceControlDto.getDeviceControl());
            var configurationsDO = new ConfigurationsDO();
            configurationsDO.setRedbackProductSn(updateDeviceControlDto.getSerialNumber());
            configurationsDO.setConfigurationType(ConfigurationType.DeviceControl.getValue());
            configurationsDO.setConfigurations(deviceControl);
            configurationsDO.setConfigurationsOnDevice(isFromDevice ? deviceControl : "{}");
            configurationsDO.setModifiedDateTime(Timestamp.from(Instant.now()));
            configurationsDO.setLastModifiedById(RequestUtil.getLoginUserEmail());
            configurationsMapper.insert(configurationsDO);
        }
    }

    /**
     * 处理电气配置更新逻辑
     *
     * @param updateElectricalConfigurationDto 更新请求DTO
     * @param product 产品配置信息
     * @param loggedInUserId 当前登录用户ID
     */
    @SneakyThrows
    private void handleElectricalConfiguration(UpdateElectricalConfigurationDTO updateElectricalConfigurationDto,
                                             ProductElectricalConfigurationVO product,
                                             String loggedInUserId) {

        ConfigurationsDO configuration = null;
        boolean isNewConfiguration = false;

        // 检查是否存在配置记录
        if (product.getConfigurationId() != null) {
            // 存在配置记录，构建ConfigurationsDO对象
            configuration = new ConfigurationsDO();
            configuration.setId(product.getConfigurationId());
            configuration.setRedbackProductSn(updateElectricalConfigurationDto.getSerialNumber());
            configuration.setConfigurationType(ConfigurationType.Electrical.getValue());
            configuration.setConfigurations(product.getConfigurations());
            configuration.setConfigurationsOnDevice(product.getConfigurationsOnDevice());
            configuration.setModifiedDateTime(product.getModifiedDateTime());
            configuration.setLastModifiedById(product.getLastModifiedById());
            configuration.setRowVersion(product.getRowVersion());
        } else {
            // 不存在配置记录，需要创建新的
            isNewConfiguration = true;
        }

        Boolean updateDeviceTwin = updateElectricalConfigurationDto.getIsFromDevice();
        String electricalJson = objectMapper.writeValueAsString(updateElectricalConfigurationDto.getElectricalConfiguration());

        if (configuration != null) {
            // 更新现有配置
            if (updateDeviceTwin) {
                configuration.setConfigurationsOnDevice(electricalJson);
                configuration.setConfigurations(electricalJson);
                applyDeviceFeedbackToGroupEvent(updateElectricalConfigurationDto);
            } else {
                // 清除GroupEventId
                updateElectricalConfigurationDto.getElectricalConfiguration().setGroupEventId(null);
                electricalJson = objectMapper.writeValueAsString(updateElectricalConfigurationDto.getElectricalConfiguration());
                configuration.setConfigurations(electricalJson);
            }

            configuration.setModifiedDateTime(Timestamp.from(Instant.now()));
            configuration.setLastModifiedById(loggedInUserId);

            // 更新配置
            configurationsMapper.updateById(configuration);
            
            // 重新查询以获取更新后的 rowVersion
            configuration = configurationsMapper.selectById(configuration.getId());
        } else {
            // 创建新配置
            configuration = new ConfigurationsDO();
            configuration.setRedbackProductSn(updateElectricalConfigurationDto.getSerialNumber());
            configuration.setConfigurationType(ConfigurationType.Electrical.getValue());

            if (updateDeviceTwin) {
                configuration.setConfigurations("{}");
                configuration.setConfigurationsOnDevice(electricalJson);
                applyDeviceFeedbackToGroupEvent(updateElectricalConfigurationDto);
            } else {
                updateElectricalConfigurationDto.getElectricalConfiguration().setGroupEventId(null);
                electricalJson = objectMapper.writeValueAsString(updateElectricalConfigurationDto.getElectricalConfiguration());
                configuration.setConfigurations(electricalJson);
                configuration.setConfigurationsOnDevice("{}");
            }

            configuration.setModifiedDateTime(Timestamp.from(Instant.now()));
            configuration.setLastModifiedById(loggedInUserId);

            // 插入新配置
            configurationsMapper.insert(configuration);
        }

        // 创建审计记录 - 这需要在保存配置后进行，以获取正确的RowVersion
        ConfigurationAuditDO auditRecord = new ConfigurationAuditDO(configuration);
        configurationAuditMapper.insert(auditRecord);

        log.info("电气配置处理完成，序列号: {}, 是否来自设备: {}, 是否新建: {}",
            updateElectricalConfigurationDto.getSerialNumber(), updateDeviceTwin, isNewConfiguration);
    }

    /**
     * 应用设备反馈到组事件
     * 对应C#中的ApplyDeviceFeedbackToGroupEvent方法
     *
     * @param updateElectricalConfigurationDto 更新请求DTO
     */
    private void applyDeviceFeedbackToGroupEvent(UpdateElectricalConfigurationDTO updateElectricalConfigurationDto) {
        try {
            // 检查电气配置中是否有GroupEventId
            if (updateElectricalConfigurationDto.getElectricalConfiguration().getGroupEventId() == null) {
                return;
            }

            // 查询组事件
            GroupEventDO groupEvent = groupEventMapper.selectByIdWithAffectedProducts(
                updateElectricalConfigurationDto.getElectricalConfiguration().getGroupEventId());

            if (groupEvent == null) {
                throw new RuntimeException(String.format("Group Event id = %d does not exist",
                    updateElectricalConfigurationDto.getElectricalConfiguration().getGroupEventId()));
            }

            // 反序列化组配置
            GroupElectricalConfiguration groupConfiguration;
            try {
                groupConfiguration = objectMapper.readValue(groupEvent.getToWhat(), GroupElectricalConfiguration.class);
            } catch (Exception e) {
                log.error("Failed to deserialize group configuration: {}", e.getMessage(), e);
                throw new RuntimeException("Failed to parse group configuration", e);
            }

            // 检查设备是否合规
            boolean isDeviceCompliant = true;

            // 检查限制导出功率
            if (groupConfiguration.getIsLimitExportPower() != null && groupConfiguration.getIsLimitExportPower() &&
                !groupConfiguration.getLimitExportPower().equals(updateElectricalConfigurationDto.getElectricalConfiguration().getLimitExportPower())) {
                isDeviceCompliant = false;
            }

            // 检查最小SoC
            if (groupConfiguration.getIsMinimumSoCChanged() != null && groupConfiguration.getIsMinimumSoCChanged() &&
                !groupConfiguration.getMinimumSoC().equals(updateElectricalConfigurationDto.getElectricalConfiguration().getMinimumSoC())) {
                isDeviceCompliant = false;
            }

            // 检查功率因数
            if (groupConfiguration.getIsPowerFactorChanged() != null && groupConfiguration.getIsPowerFactorChanged() &&
                !groupConfiguration.getPowerFactor().equals(updateElectricalConfigurationDto.getElectricalConfiguration().getPowerFactor())) {
                isDeviceCompliant = false;
            }

            // 创建产品组合规性记录
            ProductGroupComplianceDO productGroupCompliance = new ProductGroupComplianceDO();
            productGroupCompliance.setGroupEventId(groupEvent.getGroupId());
            productGroupCompliance.setRedbackProductSn(updateElectricalConfigurationDto.getSerialNumber());
            productGroupCompliance.setReceivedTimeUTC(Timestamp.from(Instant.now()));
            productGroupCompliance.setIsCompliant(isDeviceCompliant);

            // 插入合规性记录
            productGroupComplianceMapper.insert(productGroupCompliance);

            log.info("设备反馈应用到组事件完成，序列号: {}, 组事件ID: {}, 是否合规: {}",
                updateElectricalConfigurationDto.getSerialNumber(),
                groupEvent.getGroupId(),
                isDeviceCompliant);

        } catch (Exception e) {
            log.error("应用设备反馈到组事件失败，序列号: {}, 错误: {}",
                updateElectricalConfigurationDto.getSerialNumber(), e.getMessage(), e);
            throw new RuntimeException("Failed to apply device feedback to group event", e);
        }
    }

}
