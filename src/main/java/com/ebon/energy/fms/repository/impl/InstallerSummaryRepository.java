package com.ebon.energy.fms.repository.impl;

import com.ebon.energy.fms.common.enums.CommonErrorCodeEnum;
import com.ebon.energy.fms.common.exception.BizException;
import com.ebon.energy.fms.domain.entity.HardwareFamilyDO;
import com.ebon.energy.fms.domain.vo.installer.*;
import com.ebon.energy.fms.mapper.third.FleetHealthSummaryMapper;
import com.ebon.energy.fms.mapper.third.HardwareFamilyMapper;
import com.ebon.energy.fms.mapper.third.InstallationMonthMapper;
import com.ebon.energy.fms.repository.IInstallerSummaryRepository;
import com.ebon.energy.fms.util.RequestUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Repository
@RequiredArgsConstructor
public class InstallerSummaryRepository implements IInstallerSummaryRepository {

    private final HardwareFamilyMapper hardwareFamilyMapper;
    private final InstallationMonthMapper installationMonthMapper;
    private final FleetHealthSummaryMapper fleetHealthSummaryMapper;

    @Override
    public List<FleetHealthSummaryViewModel> readFleetHealthSummaryAsync(String installerId) {
        try {

            String filterByUserId = RequestUtil.getPortolUserId();

            // 处理模拟用户逻辑
            if (!StringUtils.isEmpty(installerId)) {
                // 简化版本：移除CanImpersonate检查，通过权限系统处理
                filterByUserId = installerId;
            }

            if (filterByUserId == null) {
                log.warn("Filter by user id is null");
                return List.of();
            }

            return fleetHealthSummaryMapper.getFleetHealthSummary(filterByUserId);

        } catch (Exception e) {
            log.error("获取舰队健康摘要失败，installerId: {}", installerId, e);
            throw new BizException(CommonErrorCodeEnum.ERROR, "Fetch fleet health summary failed: " + e.getMessage());
        }
    }

    @Override
    public InstallationSummaryViewModel readInstallationSummaryAsync(String installerId) {
        try {
            String filterByUserId = RequestUtil.getPortolUserId();

            // 处理模拟用户逻辑
            if (!StringUtils.isEmpty(installerId)) {
                // 简化版本：移除CanImpersonate检查，通过权限系统处理
                filterByUserId = installerId;
            }

            if (filterByUserId == null) {
                log.warn("Filter by user id is null");
                return new InstallationSummaryViewModel();
            }

            // 获取安装月份数据
            List<InstallationMonthViewModel> installationMonthData =
                installationMonthMapper.selectInstallationMonthData(filterByUserId);

            return makeInstallationMonthSummary(installationMonthData);

        } catch (Exception e) {
            log.error("获取安装摘要失败，installerId: {}", installerId, e);
            throw new BizException(CommonErrorCodeEnum.ERROR, "Fetch installation summary failed: " + e.getMessage());
        }
    }

    @Override
    public List<String> getModelsName() {
        try {
            List<HardwareFamilyDO> hardwareFamilies = hardwareFamilyMapper.selectDisplayInLargeInstaller();
            return hardwareFamilies.stream()
                    .map(HardwareFamilyDO::getFamilyDisplayName)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("获取型号名称失败", e);
            throw new BizException(CommonErrorCodeEnum.ERROR, "Fetch models name failed: " + e.getMessage());
        }
    }

    /**
     * 创建安装月份摘要
     */
    private InstallationSummaryViewModel makeInstallationMonthSummary(List<InstallationMonthViewModel> installationSummary) {
        try {
            List<HardwareFamilyDO> familyList = hardwareFamilyMapper.selectDisplayInLargeInstaller();

            // 按照指定顺序排序
            String[] requiredOrder = {"SI", "SH", "ST", "SB"};
            familyList = familyList.stream()
                    .sorted((f1, f2) -> {
                        int index1 = Arrays.asList(requiredOrder).indexOf(f1.getShortName());
                        int index2 = Arrays.asList(requiredOrder).indexOf(f2.getShortName());
                        if (index1 == -1) index1 = Integer.MAX_VALUE;
                        if (index2 == -1) index2 = Integer.MAX_VALUE;
                        return Integer.compare(index1, index2);
                    })
                    .collect(Collectors.toList());

            InstallationSummaryViewModel result = new InstallationSummaryViewModel();
            // 获取总计
            result.setTotalInstallations(makeTotalInstallationSummary(installationSummary, familyList));
            result.setQuarterlyInstallations(makeQuarterlyInstallationSummary(installationSummary, familyList));

            return result;
        } catch (Exception e) {
            log.error("创建安装月份摘要失败", e);
            throw new BizException(CommonErrorCodeEnum.ERROR, "Failed to create installation month summary: " + e.getMessage());
        }
    }

    /**
     * 创建总安装摘要
     */
    private TotalInstallationSummary makeTotalInstallationSummary(
            List<InstallationMonthViewModel> summary,
            List<HardwareFamilyDO> familyList) {

        TotalInstallationSummary totalInstallationSummary = new TotalInstallationSummary();
        totalInstallationSummary.setInstallations(new ArrayList<>());

        for (HardwareFamilyDO family : familyList) {
            // SQL查询返回的Model格式为 "DisplayName (ShortCode)"
            String expectedModel = family.getFamilyDisplayName() + " (" +
                    (family.getMarketingShortCode() != null ? family.getMarketingShortCode() : family.getShortName()) + ")";

            long installationCount = summary.stream()
                    .filter(s -> s.getModel().equals(expectedModel))
                    .count();

            TotalInstallationDetail installation = new TotalInstallationDetail();
            installation.setModel(family.getFamilyDisplayName());
            installation.setInstallationCount((int) installationCount);
            installation.setModelAbbreviation(family.getShortName());
            installation.setDisplayShortCode(
                    family.getMarketingShortCode() != null ? family.getMarketingShortCode() : family.getShortName()
            );

            totalInstallationSummary.getInstallations().add(installation);
        }

        return totalInstallationSummary;
    }

    /**
     * 创建季度安装摘要
     */
    private List<QuarterInstallationSummary> makeQuarterlyInstallationSummary(
            List<InstallationMonthViewModel> summary,
            List<HardwareFamilyDO> familyList) {

        LocalDateTime startDate = summary.isEmpty() ? LocalDateTime.now() :
                summary.stream()
                        .map(InstallationMonthViewModel::getInstallationMonth)
                        .min(LocalDateTime::compareTo)
                        .orElse(LocalDateTime.now());

        List<QuarterInstallationSummary> installationSummaries =
                generateEmptyQuarterInstallationSummaries(startDate.getYear(), LocalDateTime.now().getYear(), familyList);

        for (QuarterInstallationSummary installationSummary : installationSummaries) {
            for (QuarterInstallationSummaryReport installationReport : installationSummary.getReport()) {
                installationReport.setInstallations(
                        generateQuarterInstallationSummaryDetail(
                                summary,
                                installationSummary.getQuarter(),
                                installationSummary.getYear(),
                                installationReport.getModel()
                        )
                );
            }
        }

        return installationSummaries;
    }

    /**
     * 生成空的季度安装摘要
     */
    private List<QuarterInstallationSummary> generateEmptyQuarterInstallationSummaries(
            int startYear,
            int endYear,
            List<HardwareFamilyDO> familyList) {

        List<QuarterInstallationSummary> installationSummaries = new ArrayList<>();

        for (int year = startYear; year <= endYear; year++) {
            for (int quarter = 1; quarter <= 4; quarter++) {
                List<QuarterInstallationSummaryReport> installationReports = new ArrayList<>();

                for (HardwareFamilyDO family : familyList) {
                    QuarterInstallationSummaryReport installationReport = new QuarterInstallationSummaryReport();
                    // 设置完整的格式化模型名称以便匹配
                    String fullModelName = family.getFamilyDisplayName() + " (" +
                            (family.getMarketingShortCode() != null ? family.getMarketingShortCode() : family.getShortName()) + ")";
                    installationReport.setModel(fullModelName);
                    installationReport.setInstallations(new ArrayList<>());
                    installationReport.setModelAbbreviation(family.getShortName());
                    installationReport.setDisplayShortCode(
                            family.getMarketingShortCode() != null ? family.getMarketingShortCode() : family.getShortName()
                    );

                    installationReports.add(installationReport);
                }

                boolean isCurrentQuarter = (year == LocalDateTime.now().getYear() && quarter == getQuarter(LocalDateTime.now()));

                QuarterInstallationSummary quarterSummary = new QuarterInstallationSummary();
                quarterSummary.setYear(year);
                quarterSummary.setQuarter(quarter);
                quarterSummary.setCurrent(isCurrentQuarter);
                quarterSummary.setReport(installationReports);

                installationSummaries.add(quarterSummary);

                if (isCurrentQuarter) break;
            }
        }

        return installationSummaries;
    }

    /**
     * 生成季度安装摘要详情
     */
    private List<QuarterInstallationSummaryDetail> generateQuarterInstallationSummaryDetail(
            List<InstallationMonthViewModel> summary,
            int quarter,
            int year,
            String model) {

        List<QuarterInstallationSummaryDetail> installations = new ArrayList<>();

        for (int month : getMonthsFromQuarter(quarter)) {
            long installationCount = summary.stream()
                    .filter(s -> s.getInstallationMonth().getYear() == year
                            && s.getInstallationMonth().getMonthValue() == month
                            && s.getModel().equals(model))
                    .count();

            QuarterInstallationSummaryDetail installationDetail = new QuarterInstallationSummaryDetail();
            installationDetail.setMonth(month);
            installationDetail.setInstallationCount((int) installationCount);

            installations.add(installationDetail);
        }

        return installations;
    }

    /**
     * 获取季度
     */
    private static int getQuarter(LocalDateTime date) {
        int month = date.getMonthValue();
        if (month >= 1 && month <= 3)
            return 1;
        else if (month >= 4 && month <= 6)
            return 2;
        else if (month >= 7 && month <= 9)
            return 3;
        else
            return 4;
    }

    /**
     * 从季度获取月份
     */
    private static List<Integer> getMonthsFromQuarter(int quarter) {
        switch (quarter) {
            case 1:
                return Arrays.asList(1, 2, 3);
            case 2:
                return Arrays.asList(4, 5, 6);
            case 3:
                return Arrays.asList(7, 8, 9);
            default:
                return Arrays.asList(10, 11, 12);
        }
    }
}
