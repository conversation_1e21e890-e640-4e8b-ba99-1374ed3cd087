package com.ebon.energy.fms.repository;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ebon.energy.fms.domain.entity.AppVersionManagementDO;
import com.ebon.energy.fms.domain.po.AppVersionManagePagePO;
import com.ebon.energy.fms.domain.po.AppVersionQuery;
import com.ebon.energy.fms.domain.po.ModifyAppVersionPO;
import com.ebon.energy.fms.domain.vo.AppVersionManagementVO;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025/5/20
 */
public interface AppVersionManagementRepository {

    IPage<AppVersionManagementDO> selectAppVersionPage(AppVersionManagePagePO appVersionManagePagePo);

    List<AppVersionManagementDO> selectAllAppVersions(String keyword);

    Optional<AppVersionManagementDO> selectByProductAndOsType(AppVersionQuery appVersionQuery);

    void save(AppVersionManagementDO appversionManamentDO);

    Optional<AppVersionManagementDO> findById(Long id);

    void modify(ModifyAppVersionPO modifyAppVersionPo);
}
