package com.ebon.energy.fms.repository;

import com.ebon.energy.fms.domain.vo.installer.FleetHealthSummaryViewModel;
import com.ebon.energy.fms.domain.vo.installer.InstallationSummaryViewModel;

import java.util.List;

public interface IInstallerSummaryRepository {

    List<FleetHealthSummaryViewModel> readFleetHealthSummaryAsync(String installerId);

    InstallationSummaryViewModel readInstallationSummaryAsync(String installerId);

    List<String> getModelsName();
}
