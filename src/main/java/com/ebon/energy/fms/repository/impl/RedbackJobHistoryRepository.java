package com.ebon.energy.fms.repository.impl;

import com.ebon.energy.fms.common.enums.RedbackJobs;
import com.ebon.energy.fms.config.PortalConfig;
import com.ebon.energy.fms.domain.vo.charts.RedbackJobHistory;
import com.ebon.energy.fms.mapper.third.RedbackJobHistoryMapper;
import com.ebon.energy.fms.repository.IRedbackJobHistoryRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Repository
@RequiredArgsConstructor
public class RedbackJobHistoryRepository implements IRedbackJobHistoryRepository {

    private final RedbackJobHistoryMapper redbackJobHistoryMapper;
    private final PortalConfig portalConfig;

    @Override
    public RedbackJobHistory getLatestJobHistoryAsync(RedbackJobs job) {
        try {
            return redbackJobHistoryMapper.getLatestJobHistoryByJobName(job.toString());
        } catch (Exception e) {
            log.error("获取作业历史记录失败，作业名称: {}", job, e);
            return null;
        }
    }

    @Override
    public List<RedbackJobHistory> getStorageReductionJobsAsync() {
        List<RedbackJobHistory> storageReductionJobs = new ArrayList<>();

        String listOfStorageReductionJobs = portalConfig.getListOfStorageReductionJobs();
        if (!StringUtils.hasText(listOfStorageReductionJobs)) {
            return storageReductionJobs;
        }

        // 处理字符串分隔符：空格、逗号、分号、斜杠、管道符
        String spaceSeparatedList = listOfStorageReductionJobs
                .replace(',', ' ')
                .replace(';', ' ')
                .replace('/', ' ')
                .replace('|', ' ');

        // 去除多余空格
        spaceSeparatedList = spaceSeparatedList.replaceAll("\\s+", " ").trim();

        // 分割并处理每个作业字符串
        List<String> jobStrings = Arrays.stream(spaceSeparatedList.split(" "))
                .filter(StringUtils::hasText)
                .collect(Collectors.toList());

        for (String jobString : jobStrings) {
            try {
                RedbackJobs job = RedbackJobs.valueOf(jobString);
                RedbackJobHistory jobHistory = getLatestJobHistoryAsync(job);

                if (jobHistory != null) {
                    storageReductionJobs.add(jobHistory);
                }
            } catch (IllegalArgumentException e) {
                log.warn("无效的作业名称: {}", jobString);
            } catch (Exception e) {
                log.error("处理作业时发生错误，作业名称: {}", jobString, e);
            }
        }

        return storageReductionJobs;
    }
}
