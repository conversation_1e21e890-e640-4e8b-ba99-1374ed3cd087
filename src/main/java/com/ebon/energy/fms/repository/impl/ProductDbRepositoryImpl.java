package com.ebon.energy.fms.repository.impl;

import com.alibaba.fastjson.JSONObject;
import com.ebon.energy.fms.common.enums.ConfigurationType;
import com.ebon.energy.fms.common.exception.BizException;
import com.ebon.energy.fms.common.utils.RedbackDataUtils;
import com.ebon.energy.fms.domain.entity.InverterScheduleDO;
import com.ebon.energy.fms.domain.vo.ElectricalConfigurationVO;
import com.ebon.energy.fms.domain.entity.InverterRoleSiteIdDO;
import com.ebon.energy.fms.domain.vo.Watt;
import com.ebon.energy.fms.domain.vo.product.control.ScheduleAuditDto;
import com.ebon.energy.fms.domain.vo.product.control.ScheduleInfoDto;
import com.ebon.energy.fms.domain.vo.site.ProductDailyCacheModel;
import com.ebon.energy.fms.domain.vo.telemetry.SystemStatus;
import com.ebon.energy.fms.mapper.third.*;
import com.ebon.energy.fms.repository.ProductDbRepository;
import com.ebon.energy.fms.service.factory.AliyunTableStoreService;
import com.ebon.energy.fms.service.factory.AzureTableStorageService;
import com.ebon.energy.fms.util.RequestUtil;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.sql.Time;
import java.sql.Timestamp;
import java.time.LocalTime;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;
import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;

/**
 * Repository interface for product database operations
 */
@Repository
@RequiredArgsConstructor
@Slf4j
public class ProductDbRepositoryImpl implements ProductDbRepository {

    private final ProductMapper productMapper;
    private final ConfigurationsMapper configurationsMapper;
    private final InverterScheduleMapper inverterScheduleMapper;
    private final ProductInstallationMapper productInstallationMapper;
    private final SiteMapper siteMapper;
    private final ProductDailyCachesMapper productDailyCachesMapper;

    private final AliyunTableStoreService tuyaTableStoreService;

    private final AzureTableStorageService azureTableStorageService;

    // 共享线程池，避免每次创建新的线程池
    private ExecutorService sharedExecutorService;

    /**
     * 初始化共享线程池
     */
    @PostConstruct
    public void initializeThreadPool() {
        // 创建一个固定大小的线程池，大小为CPU核心数的2倍，最大不超过20
        int poolSize = Math.min(Runtime.getRuntime().availableProcessors() * 2, 20);
        this.sharedExecutorService = Executors.newFixedThreadPool(poolSize, r -> {
            Thread thread = new Thread(r, "SystemStatus-Query-Thread");
            thread.setDaemon(true); // 设置为守护线程
            return thread;
        });
        log.info("初始化共享线程池，线程池大小: {}", poolSize);
    }

    /**
     * 销毁共享线程池
     */
    @PreDestroy
    public void destroyThreadPool() {
        if (sharedExecutorService != null && !sharedExecutorService.isShutdown()) {
            log.info("正在关闭共享线程池...");
            sharedExecutorService.shutdown();
            try {
                // 等待30秒让任务完成
                if (!sharedExecutorService.awaitTermination(30, TimeUnit.SECONDS)) {
                    log.warn("线程池未能在30秒内正常关闭，强制关闭");
                    sharedExecutorService.shutdownNow();
                }
            } catch (InterruptedException e) {
                log.warn("等待线程池关闭时被中断", e);
                sharedExecutorService.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }

    @Override
    public List<ScheduleAuditDto> getActiveScheduleCreateAuditsTrustedAsync(String serialNumber) {
        return configurationsMapper.getActiveScheduleCreateAudits(serialNumber);
    }

    @Override
    public void deleteScheduleTrustedAsync(String scheduleId, String deletedById, String serialNumber, String channel) {
        // Implement the C# functionality in Java
        Date whenUtc = new Date(); // Current UTC time

        int rowsAffected = inverterScheduleMapper.deleteScheduleTrustedAsync(
                deletedById,
                whenUtc,
                channel,
                serialNumber,
                scheduleId
        );

        if (rowsAffected > 1) {
            String msg = String.format("Unexpected number of rows when updating schedule: %s/%s/%s: %d",
                    scheduleId, deletedById, serialNumber, rowsAffected);
            log.warn(msg);
            // Don't log an issue if it's zero as there is a large number of existing schedules that are not in this table.
        }
    }

    @Override
    public void saveScheduleTrustedAsync(String userId, String serialNumber, String scheduleId, Object schedule,
                                         String ianaTimeZoneId, String channel, String userNotes) {
        if (!(schedule instanceof ScheduleInfoDto)) {
            log.error("Schedule object is not of type ScheduleInfoDto");
            return;
        }

        ScheduleInfoDto scheduleInfo = (ScheduleInfoDto) schedule;
        Date createdOnUtc = new Date(); // Current UTC time

        // Convert ZonedDateTime to java.sql.Date
        Date startAtUtc = scheduleInfo.getStartAtUtc() != null ?
                Date.from(scheduleInfo.getStartAtUtc().toInstant()) : null;
        Date endAtUtc = scheduleInfo.getEndAtUtc() != null ?
                Date.from(scheduleInfo.getEndAtUtc().toInstant()) : null;

        // Convert Duration to java.sql.Time
        Time dailyStartTime = null;
        if (scheduleInfo.getDailyStartTime() != null) {
            LocalTime localTime = LocalTime.MIDNIGHT.plus(scheduleInfo.getDailyStartTime());
            dailyStartTime = Time.valueOf(localTime);
        }

        Time duration = null;
        if (scheduleInfo.getScheduleDuration() != null) {
            LocalTime localTime = LocalTime.MIDNIGHT.plus(scheduleInfo.getScheduleDuration());
            duration = Time.valueOf(localTime);
        }

        // Convert ScheduleDays to Integer
        Integer daysOfWeek = scheduleInfo.getDaysOfWeekActive() != null ?
                (int) scheduleInfo.getDaysOfWeekActive().getValue() : 0;

        // Convert action parameter from long to int
        Integer actionParameter = (int) scheduleInfo.getActionParameter();

        // Create InverterScheduleDO object
        InverterScheduleDO inverterSchedule = new InverterScheduleDO();
        inverterSchedule.setSerialNumber(serialNumber);
        inverterSchedule.setScheduleId(scheduleId);
        inverterSchedule.setUserNotes(userNotes);
        inverterSchedule.setCreatedById(RequestUtil.getPortolUserId());
        inverterSchedule.setCreatedOnUtc(createdOnUtc == null ? null : new Timestamp(createdOnUtc.getTime()));
        inverterSchedule.setCreatedChannel(channel);
        inverterSchedule.setStartAtUtc(startAtUtc == null ? null : new Timestamp(startAtUtc.getTime()));
        inverterSchedule.setEndAtUtc(endAtUtc == null ? null : new Timestamp(endAtUtc.getTime()));
        inverterSchedule.setDailyStartTime(dailyStartTime);
        inverterSchedule.setDuration(duration);
        inverterSchedule.setDaysOfWeek(daysOfWeek);
        inverterSchedule.setActionName(scheduleInfo.getActionName());
        inverterSchedule.setActionParameter(actionParameter);
        inverterSchedule.setPriority(scheduleInfo.getPriority());
        inverterSchedule.setIanaTimeZoneId(ianaTimeZoneId);

        // Use the insert method from BaseMapper
        int rowsAffected = inverterScheduleMapper.insert(inverterSchedule);

        if (rowsAffected != 1) {
            log.warn("Unexpected number of rows affected when inserting schedule: {}", rowsAffected);
        }
    }

    @Override
    public Watt getPVSizeAsync(String serialNumber) {
        List<String> data = productInstallationMapper.selectPVSize(serialNumber);
        String pvSize1 = data.size() > 0 ? data.get(0) : null;
        String pvSize2 = data.size() > 1 ? data.get(1) : null;
        String pvSize3 = data.size() > 2 ? data.get(2) : null;
        return new Watt(RedbackDataUtils.getPVSizeW(pvSize1, pvSize2, pvSize3));
    }

    @Override
    public ElectricalConfigurationVO getElectricalConfigurationRoss1(String serialNumber) {
        if (StringUtils.isBlank(serialNumber)) {
            throw new BizException("serialNumber not exist");
        }

        String conf = configurationsMapper.selectConfigurationByType(serialNumber, ConfigurationType.Electrical.getValue());
        ElectricalConfigurationVO configurationVO = JSONObject.parseObject(conf, ElectricalConfigurationVO.class);
        if (configurationVO != null) {
            configurationVO.patchLegacyBatteryInfo();
        }

        return configurationVO;
    }

    @Override
    public List<InverterRoleSiteIdDO> getSiteInfoByProducts(List<String> sns) {
        return productMapper.selectSiteInfoBySerialNumbers(sns);
    }

    @Override
    public List<ProductDailyCacheModel> getNDayProductDailyCacheTrusted(
            String serialNumber,
            Timestamp startTime,
            Timestamp endTime
    ){
        var productDailyHistoryDOS = productDailyCachesMapper.selectEnergyDailyHistory(serialNumber, startTime, endTime);

        return productDailyHistoryDOS.stream()
                .map(it -> it.asProductDailyCache(serialNumber))
                .collect(Collectors.toList());
    }

    @Override
    @SneakyThrows
    public List<SystemStatus> getSystemStatusesInRangeAsync(String serialNumber, long startEpoch, long endEpoch) {
        log.debug("开始查询系统状态数据，设备序列号: {}, 开始时间: {}, 结束时间: {}",
                 serialNumber, startEpoch, endEpoch);

        long startTime = System.currentTimeMillis();
        int secondsInDay = 86400;

        // 使用CompletableFuture替代Callable，提供更好的异步控制
        List<CompletableFuture<List<SystemStatus>>> futures = new ArrayList<>();

        for (long date = startEpoch - 1; date <= endEpoch - 1; date += secondsInDay) {
            long end = date + secondsInDay;
            if (endEpoch < end) {
                end = endEpoch;
            }
            long finalDate = date;
            long finalEnd = end;

            // 为每个时间段创建两个异步任务
            CompletableFuture<List<SystemStatus>> tuyaFuture = CompletableFuture
                .supplyAsync(() -> {
                    try {
                        return tuyaTableStoreService.getDataInRange(serialNumber, finalDate, finalEnd);
                    } catch (Exception e) {
                        log.warn("Tuya存储查询失败，设备: {}, 时间段: {}-{}, 错误: {}",
                                serialNumber, finalDate, finalEnd, e.getMessage());
                        return new ArrayList<SystemStatus>();
                    }
                }, sharedExecutorService);

            CompletableFuture<List<SystemStatus>> azureFuture = CompletableFuture
                .supplyAsync(() -> {
                    try {
                        return azureTableStorageService.getDataInRangeAsync(serialNumber, finalDate, finalEnd);
                    } catch (Exception e) {
                        log.warn("Azure存储查询失败，设备: {}, 时间段: {}-{}, 错误: {}",
                                serialNumber, finalDate, finalEnd, e.getMessage());
                        return new ArrayList<SystemStatus>();
                    }
                }, sharedExecutorService);

            futures.add(tuyaFuture);
            futures.add(azureFuture);
        }

        // 等待所有任务完成，确保数据完整性
        CompletableFuture<Void> allFutures = CompletableFuture.allOf(
            futures.toArray(new CompletableFuture[0])
        );

        // 设置超时时间，避免无限等待
        try {
            allFutures.get(300, TimeUnit.SECONDS); // 60秒超时
        } catch (TimeoutException e) {
            log.error("查询系统状态数据超时，设备: {}", serialNumber);
            throw new RuntimeException("查询系统状态数据超时", e);
        }

        // 收集所有结果
        List<SystemStatus> allStatuses = new ArrayList<>();
        for (CompletableFuture<List<SystemStatus>> future : futures) {
            try {
                List<SystemStatus> result = future.get();
                if (result != null && !result.isEmpty()) {
                    allStatuses.addAll(result);
                }
            } catch (Exception e) {
                log.warn("获取查询结果失败: {}", e.getMessage());
            }
        }

        log.debug("原始数据收集完成，总记录数: {}", allStatuses.size());

        // 按 date 去重并排序
        Map<ZonedDateTime, SystemStatus> uniqueByDate = allStatuses.stream()
                .filter(Objects::nonNull)
                .filter(status -> status.getDate() != null)
                .collect(Collectors.toMap(
                        SystemStatus::getDate,
                        s -> s,
                        (s1, s2) -> s1 // 保留第一个
                ));

        List<SystemStatus> result = uniqueByDate.values().stream()
                .sorted(Comparator.comparing(SystemStatus::getDate))
                .collect(Collectors.toList());

        long endTime = System.currentTimeMillis();
        log.info("系统状态数据查询完成，设备: {}, 去重后记录数: {}, 耗时: {}ms",
                serialNumber, result.size(), (endTime - startTime));

        return result;
    }

}
