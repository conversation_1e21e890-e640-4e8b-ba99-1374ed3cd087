package com.ebon.energy.fms.repository;

import com.ebon.energy.fms.common.enums.RedbackJobs;
import com.ebon.energy.fms.domain.vo.charts.RedbackJobHistory;

import java.util.List;

public interface IRedbackJobHistoryRepository {

    /**
     * 根据作业获取最新的作业历史记录
     *
     * @param job 作业枚举
     * @return 作业历史记录
     */
    RedbackJobHistory getLatestJobHistoryAsync(RedbackJobs job);

    /**
     * 获取存储缩减作业列表
     *
     * @return 存储缩减作业历史记录列表
     */
    List<RedbackJobHistory> getStorageReductionJobsAsync();

}
