package com.ebon.energy.fms.repository.impl;

import com.ebon.energy.fms.domain.vo.Watt;
import com.ebon.energy.fms.domain.vo.site.SolarAndBackupPairVO;
import com.ebon.energy.fms.domain.vo.telemetry.BackupLoadStatus;
import com.ebon.energy.fms.domain.vo.telemetry.PVStatus;
import com.ebon.energy.fms.domain.vo.telemetry.SystemStatus;
import com.ebon.energy.fms.repository.TelemetryRepository;
import com.ebon.energy.fms.service.factory.AzureTableStorageService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.time.Instant;
import java.util.List;
import java.util.OptionalDouble;

import static com.ebon.energy.fms.util.SafeAccess.getOrElse;

@Slf4j
@Repository
public class AzureStorageTelemetryRepository implements TelemetryRepository {

    @Resource
    private AzureTableStorageService azureTableStorageService;

    @Override
    public SolarAndBackupPairVO getDeviceAverageBackupAndSolarPowerBetween(String serialNumber, Instant startUtc, Instant endUtc) {
        List<SystemStatus> statuses = azureTableStorageService.getDataInRangeAsync(serialNumber, startUtc.getEpochSecond(), endUtc.getEpochSecond());
        if (CollectionUtils.isNotEmpty(statuses)) {
            OptionalDouble averageBackupPowerOptional = statuses.stream()
                    .mapToDouble(status -> getOrElse(status.getBackupLoad(), BackupLoadStatus::getP, 0.0))
                    .average();

            OptionalDouble averageSolarPowerOptional = statuses.stream()
                    .mapToDouble(status -> getOrElse(status.getPV(), PVStatus::getP, 0.0))
                    .average();

            Watt averageBackupPowerOverLastHourInWatts = new Watt(averageBackupPowerOptional.orElse(0));
            Watt averageSolarPowerOverLastHourInWatts = new Watt(averageSolarPowerOptional.orElse(0));

            SystemStatus last = statuses.get(statuses.size() - 1);

            return new SolarAndBackupPairVO(
                    last.getDate().toInstant(),
                    averageBackupPowerOverLastHourInWatts,
                    averageSolarPowerOverLastHourInWatts
            );
        }

        return null;
    }
}
