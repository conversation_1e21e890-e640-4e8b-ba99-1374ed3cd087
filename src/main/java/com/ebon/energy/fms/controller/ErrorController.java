package com.ebon.energy.fms.controller;

import com.ebon.energy.fms.domain.vo.ErrorMappingVO;
import com.ebon.energy.fms.domain.vo.JsonResult;
import com.ebon.energy.fms.service.ErrorService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 错误信息
 */
@RestController
@RequestMapping("/api/error")
public class ErrorController {

    @Resource
    private ErrorService errorService;

    /**
     * 错误枚举列表
     *
     * @return
     */
    @GetMapping("/list")
    public JsonResult<List<ErrorMappingVO>> list() {
        return JsonResult.buildSuccess(errorService.getErrorMappings());
    }

}
