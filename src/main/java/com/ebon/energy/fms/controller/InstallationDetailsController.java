package com.ebon.energy.fms.controller;

import com.alibaba.fastjson.JSONObject;
import com.ebon.energy.fms.common.constants.UIStrings;
import com.ebon.energy.fms.common.enums.AustralianState;
import com.ebon.energy.fms.common.enums.InstallationDetails;
import com.ebon.energy.fms.common.enums.PanelDirection;
import com.ebon.energy.fms.common.enums.UserPreferenceKey;
import com.ebon.energy.fms.config.PortalConfig;
import com.ebon.energy.fms.common.utils.*;
import com.ebon.energy.fms.domain.entity.AddressesDO;
import com.ebon.energy.fms.domain.vo.AddressDTO;
import com.ebon.energy.fms.domain.vo.JsonResult;
import com.ebon.energy.fms.domain.vo.ProductRegistrationDTO;
import com.ebon.energy.fms.domain.vo.detail.*;
import com.ebon.energy.fms.domain.vo.product.control.HardwareFirmwareSpecification;
import com.ebon.energy.fms.domain.vo.product.control.InstallationSpecification;
import com.ebon.energy.fms.service.ICountryStateFacadeService;
import com.ebon.energy.fms.service.INMIService;
import com.ebon.energy.fms.service.repository.IRedbackRepository;
import com.ebon.energy.fms.service.repository.ISpecificationRepository;
import com.ebon.energy.fms.service.repository.ISiteRepository;
import com.ebon.energy.fms.util.RequestUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.ValidationException;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * 安装详情控制器
 * 处理产品安装详情的相关操作
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/installation-details")
public class InstallationDetailsController {

    private final IGeoUtility geoUtility;

    private final PortalConfig portalConfig;

    private final IRedbackRepository repository;

    private final INMIService nmiService;

    private final ISiteRepository siteRepository;

    private final ISpecificationRepository specificationRepository;

    private final ICountryStateFacadeService countryStateFacadeService;

    // 常量定义
    private static final String USER_DEFINED = "UserDefined";
    private static final String SERIAL_NUMBER_NOT_SUPPLIED = "No serial number supplied.";
    private static final String ARRAY_1 = "Solar Input 1";
    private static final String ARRAY_2 = "Solar Input 2";
    private static final String ARRAY_3 = "Solar Input 3";

    @GetMapping("/configure")
    public ResponseEntity<InstallationDetailsViewModel> configure(
            @RequestParam String serialNumber) {

        var userId = RequestUtil.getPortolUserId();
        try {
            InstallationDetailsViewModel model = createViewModel(userId, serialNumber);
            return ResponseEntity.ok(model);
        } catch (ValidationException e) {
            log.error("Validation error in configure method", e);
            return ResponseEntity.badRequest().build();
        } catch (Exception e) {
            log.error("Error in configure method", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * 配置安装详情（POST）
     *
     * @param model         安装详情视图模型
     * @param bindingResult 验证结果
     * @param userId        用户ID（从认证上下文获取）
     * @return 响应结果
     */
    @PostMapping("/configure")
    public JsonResult<Object> configurePost(@Valid @RequestBody InstallationDetailsViewModel model) {
        var userId = RequestUtil.getPortolUserId();


        // 验证地址PlaceId
        if (model.getAddress() == null ||
                (model.getAddress().getPlaceId() == null || model.getAddress().getPlaceId().trim().isEmpty())) {
            return JsonResult.buildError(JSONObject.toJSONString("Please select an address from the dropdown menu"));
        }

        // NMI验证
        AustralianState state = StateValidator.tryParse(
                model.getAddress() != null ? model.getAddress().getState() : null);

        if (!model.isNMIOptOut() && model.isHasEditOnInstallationDetails()) {
            var nmiValidation = nmiService.validationForState(
                    model.getConnectionPointIdentifier(),
                    state,
                    model.getInstallationStartDate());

            model.setIsNMIValid(nmiValidation.isValid());
            model.setNMIMandatory(nmiValidation.isMandatory());
            model.setLastDateNMIMandatoryRequired(nmiValidation.getLastDateNMIMandatoryRequired());

            if (model.getIsNMIValid() != null && !model.getIsNMIValid()) {
                return JsonResult.buildError("Incorrect or missing NMI, please check and try again.");
            }
        }

         processInstallationUpdate(model, userId, true);
        return JsonResult.buildSuccess();
    }

    /**
     * 获取用户偏好设置
     *
     * @param serialNumber 序列号
     * @param userId       用户ID
     * @return 偏好设置视图模型
     */
    private PreferencesViewModel getPreferences(String serialNumber, String userId) {
        PreferencesViewModel model = new PreferencesViewModel();
        model.setSerialNumber(serialNumber);

        String friendlyName = "";
        if (portalConfig.isRefreshFeatureEnabled()) {
            Map<UserPreferenceKey, String> preferences = repository.getUserPreferences(userId, serialNumber);
            friendlyName = preferences.getOrDefault(UserPreferenceKey.ProductFriendlyName, "");
        }

        model.setFriendlyName(friendlyName);
        return model;
    }

    /**
     * 尝试更新用户偏好设置
     *
     * @param friendlyName 友好名称
     * @param serialNumber 序列号
     * @param userId       用户ID
     */
    private void tryUpdatePreferences(String friendlyName, String serialNumber, String userId) {
        try {
            Map<UserPreferenceKey, String> preferences = new HashMap<>();
            preferences.put(UserPreferenceKey.ProductFriendlyName, friendlyName);
            repository.updateUserPreferences(userId, serialNumber, preferences);
        } catch (ValidationException e) {
            log.error("Validation failed: {}", e.getMessage(), e);
        } catch (Exception e) {
            log.error("Invalid operation: {}", e.getMessage(), e);
        }
    }

    /**
     * 处理安装信息更新的通用方法
     *
     * @param model           安装详情视图模型
     * @param userId          用户ID
     * @param isConfigureMode 是否为配置模式
     * @return 响应结果
     */
    private void processInstallationUpdate(InstallationDetailsViewModel model, String userId, boolean isConfigureMode) {

        Map<String, Object> response = new HashMap<>();

        // 创建ProductRegistrationDTO
        var installation = createProductRegistrationDTO(model);

        // 获取地理编码信息
        var googleMetaData = geoUtility.geoCodeAddress(model.getAddress());
        if (googleMetaData == null && model.getAddress() != null &&
                model.getAddress().getLatitude() != null && model.getAddress().getLongitude() != null) {
            googleMetaData = new com.ebon.energy.fms.domain.vo.AddressDTO();
            googleMetaData.setLatitude(model.getAddress().getLatitude());
            googleMetaData.setLongitude(model.getAddress().getLongitude());
        }

        if (googleMetaData != null) {
            String timeZoneId = geoUtility.timeZoneAddress(googleMetaData);
            googleMetaData.setTimeZoneId(timeZoneId);
        }

        // 设置安装地址
        installation.setInstallationAddress(createAddress(model, googleMetaData));

        // 更新安装信息
        if (isConfigureMode) {
            if (repository.canUpdateInstallation(userId, installation)) {
                repository.updateRedbackInstallation(userId, installation);
            }
        } else {
            repository.updateRedbackInstallation(userId, installation);
        }

        // 更新偏好设置
        if (portalConfig.isRefreshFeatureEnabled() || !isConfigureMode) {
            tryUpdatePreferences(model.getFriendlyName(), model.getSerialNumber(), userId);
        }

    }

    /**
     * 创建ProductRegistrationDTO
     *
     * @param model 安装详情视图模型
     * @return ProductRegistrationDTO
     */
    private ProductRegistrationDTO createProductRegistrationDTO(InstallationDetailsViewModel model) {
        ProductRegistrationDTO installation = new ProductRegistrationDTO();
        installation.setSerialNumber(model.getSerialNumber());
        installation.setSolarPanelManufacturer(model.getSolarPanelManufacturer().toString());
        installation.setSolarPanelType(model.getSolarPanelType());
        installation.setOffgrid(!model.isOnGrid());
        installation.setConnectionPointIdentifier(model.getConnectionPointIdentifier());
        installation.setConnectionPointIdentifierOptOut(model.isNMIOptOut());

        // 转换太阳能板信息
        if (model.getSolarPanels() != null) {
            var solarPanels = model.getSolarPanels().stream()
                    .map(panel -> {
                        SolarPanel solarPanel = new SolarPanel();
                        solarPanel.setPanelDirection(panel.getPanelDirection() != null ?
                                panel.getPanelDirection() : PanelDirection.None);
                        solarPanel.setNumberOfPanels(panel.getNumberOfPanels() != null ?
                                panel.getNumberOfPanels() : 0);
                        solarPanel.setPvSize(panel.getPvSize() != null ?
                                panel.getPvSize() : 0.0);
                        return solarPanel;
                    })
                    .collect(java.util.stream.Collectors.toList());
            installation.setSolarPanels(solarPanels);
        }

        return installation;
    }

    /**
     * 创建地址对象
     *
     * @param model          安装详情视图模型
     * @param googleMetaData 谷歌地理编码数据
     * @return Address对象
     */
    private AddressesDO createAddress(InstallationDetailsViewModel model, IRedbackAddress googleMetaData) {
        AddressesDO address = new AddressesDO();

        if (model.getAddress() != null) {
            address.setAddressLineOne(AddressUtility.compressStreetComponents(
                    model.getAddress().getStreetNumber(),
                    model.getAddress().getStreetName()));
            address.setSuburb(model.getAddress().getSuburb());
            address.setState(model.getAddress().getState());
            address.setPostCode(model.getAddress().getPostCode());
            address.setCountry(model.getAddress().getCountry());

            // 处理经纬度
            String latitude = "0".equals(model.getAddress().getLatitude()) ?
                    (googleMetaData != null ? googleMetaData.getLatitude() : model.getAddress().getLatitude()) :
                    model.getAddress().getLatitude();
            String longitude = "0".equals(model.getAddress().getLongitude()) ?
                    (googleMetaData != null ? googleMetaData.getLongitude() : model.getAddress().getLongitude()) :
                    model.getAddress().getLongitude();

            address.setLatitude(latitude);
            address.setLongitude(longitude);

            // 处理GooglePlaceId
            String placeId = USER_DEFINED.equals(model.getAddress().getPlaceId()) ?
                    (googleMetaData != null ? googleMetaData.getGooglePlaceId() : null) :
                    model.getAddress().getPlaceId();
            address.setGooglePlaceId(placeId);

            // 设置时区
            String timeZoneId = googleMetaData != null ? googleMetaData.getTimeZoneId() :
                    (model.getAddress().getTimeZoneId() != null ? model.getAddress().getTimeZoneId() : "");
            address.setTimeZoneId(timeZoneId);
        }

        return address;
    }

    /**
     * 创建安装详情视图模型
     *
     * @param userId       用户ID
     * @param serialNumber 序列号
     * @return 安装详情视图模型
     * @throws ValidationException 当序列号为空时抛出
     */
    public InstallationDetailsViewModel createViewModel(String userId, String serialNumber) throws ValidationException {
        if (serialNumber == null || serialNumber.trim().isEmpty()) {
            throw new ValidationException(SERIAL_NUMBER_NOT_SUPPLIED);
        }

        // 获取安装信息
        RedbackInstallation repositoryModel = repository.getRedbackInstallation(userId, serialNumber);

        // 辅助方法：根据名称获取安装详情
        java.util.function.Function<InstallationDetails, RedbackProductInstallationDetail> getDetail =
                detail -> repositoryModel.getRedbackProductInstallationDetails().stream()
                        .filter(x -> detail.getKey().equals(x.getName()))
                        .findFirst()
                        .orElse(null);

        // 获取各种安装详情
        RedbackProductInstallationDetail solarPanelManufacturerDetail = getDetail.apply(InstallationDetails.SolarPanelManufacturer);
        RedbackProductInstallationDetail solarPanelTypeDetail = getDetail.apply(InstallationDetails.SolarPanelType);
        RedbackProductInstallationDetail isOffGridDetail = getDetail.apply(InstallationDetails.IsOffgrid);

        // 获取太阳能板信息
        var panelInfos = new Object[][]{
                {ARRAY_1, getDetail.apply(InstallationDetails.NumberOfPanels1),
                        getDetail.apply(InstallationDetails.PVSize1), getDetail.apply(InstallationDetails.PanelDirection1)},
                {ARRAY_2, getDetail.apply(InstallationDetails.NumberOfPanels2),
                        getDetail.apply(InstallationDetails.PVSize2), getDetail.apply(InstallationDetails.PanelDirection2)},
                {ARRAY_3, getDetail.apply(InstallationDetails.NumberOfPanels3),
                        getDetail.apply(InstallationDetails.PVSize3), getDetail.apply(InstallationDetails.PanelDirection3)}
        };

        // 获取连接点信息
        RedbackProductInstallationDetail connectionPointIdentifierDetail = getDetail.apply(InstallationDetails.ConnectionPointIdentifier);
        RedbackProductInstallationDetail connectionPointIdentifierOptOutDetail = getDetail.apply(InstallationDetails.ConnectionPointIdentifierOptOut);
        boolean isNMIOptOut = connectionPointIdentifierOptOutDetail != null &&
                SolarPanelsUtility.parseToBool(connectionPointIdentifierOptOutDetail.getValue());

        return createViewModelFromData(repositoryModel, solarPanelManufacturerDetail, solarPanelTypeDetail,
                isOffGridDetail, panelInfos, connectionPointIdentifierDetail, isNMIOptOut, userId, serialNumber);
    }

    /**
     * 从数据创建视图模型
     */
    private InstallationDetailsViewModel createViewModelFromData(
            RedbackInstallation repositoryModel,
            RedbackProductInstallationDetail solarPanelManufacturerDetail,
            RedbackProductInstallationDetail solarPanelTypeDetail,
            RedbackProductInstallationDetail isOffGridDetail,
            Object[][] panelInfos,
            RedbackProductInstallationDetail connectionPointIdentifierDetail,
            boolean isNMIOptOut,
            String userId,
            String serialNumber) {

        try {
            // 获取系统状态和规格信息
            var systemStatus = JSONObject.parseObject(repositoryModel.getRedbackProduct().getLatestSystemStatus(), com.ebon.energy.fms.domain.vo.telemetry.SystemStatus.class);

            HardwareFirmwareSpecification productDefaults = specificationRepository.getSpecAsync(userId, serialNumber);
            InstallationSpecification installationSpec = specificationRepository.getInstallationSpecAsync(userId, serialNumber);

            // 获取CSIP LFDI
            String csipLfdi = siteRepository.getSiteLfdi(repositoryModel.getSiteId());

            // 获取国家州信息
            var countryStates = countryStateFacadeService.getAllStates(null);

            // 创建视图模型
            InstallationDetailsViewModel model = new InstallationDetailsViewModel();
            model.setSolarPanelManufacturer(SolarPanelsUtility.parseSolarPanelManufacturer(
                    solarPanelManufacturerDetail != null ? solarPanelManufacturerDetail.getValue() : null));
            model.setSerialNumber(repositoryModel.getRedbackProductSn());
            model.setMustHaveGrid(installationSpec.isMustHaveGrid());
            model.setOnGrid(isOffGridDetail != null ? !SolarPanelsUtility.parseToBool(isOffGridDetail.getValue()) :
                    productDefaults.isSupportsConnectedPV());
            model.setConnectionPointIdentifier(connectionPointIdentifierDetail != null ?
                    connectionPointIdentifierDetail.getValue() : null);
            model.setNMIOptOut(isNMIOptOut);
            model.setInstalledProductSupportsConnectedPV(productDefaults.isSupportsConnectedPV());
            model.setSolarPanelType(solarPanelTypeDetail != null ? solarPanelTypeDetail.getValue() : null);

            // 设置地址信息
            model.setAddress(createAddressViewModel(repositoryModel.getAddress(), countryStates));

            // 设置太阳能板信息
            model.setSolarPanels(createSolarPanelViewModels(panelInfos));

            // 设置照片和其他信息
            model.setPhotos(repository.getPhotosForAsync(userId, serialNumber));
            model.setFriendlyName(getPreferences(serialNumber, userId).getFriendlyName());
            model.setInstallationStartDate(repositoryModel.getInstallationStartDate());
            model.setCsipLfdi(csipLfdi);

            return model;
        } catch (Exception e) {
            log.error("Error creating view model", e);
            throw new RuntimeException("Failed to create view model", e);
        }
    }

    /**
     * 创建地址视图模型
     */
    private InstallationAddressViewModel createAddressViewModel(AddressesDO address, java.util.List<CountryStateDTO> countryStates) {
        if (address == null) {
            return null;
        }

        InstallationAddressViewModel addressViewModel = new InstallationAddressViewModel();
        addressViewModel.setPlaceId(address.getGooglePlaceId());
        addressViewModel.setStreetNumber(address.getAddressLineOne());
        addressViewModel.setSuburb(address.getSuburb());
        addressViewModel.setState(address.getState());
        addressViewModel.setPostCode(address.getPostCode());
        addressViewModel.setCountry(address.getCountry());
        addressViewModel.setLatitude(address.getLatitude());
        addressViewModel.setLongitude(address.getLongitude());
        addressViewModel.setFullAddress(AddressUtility.formattedAddress(address));
        addressViewModel.setTimeZoneId(address.getTimeZoneId());
        addressViewModel.setCountryStates(countryStates);

        return addressViewModel;
    }

    /**
     * 创建太阳能板视图模型列表
     */
    private java.util.List<SolarPanelViewModel> createSolarPanelViewModels(Object[][] panelInfos) {
        return java.util.Arrays.stream(panelInfos)
                .map(panelInfo -> {
                    SolarPanelViewModel panelViewModel = new SolarPanelViewModel();
                    panelViewModel.setArrayName((String) panelInfo[0]);

                    RedbackProductInstallationDetail numberOfPanelsDetail = (RedbackProductInstallationDetail) panelInfo[1];
                    panelViewModel.setNumberOfPanels(numberOfPanelsDetail != null ?
                            SolarPanelsUtility.parseToInt(numberOfPanelsDetail.getValue()) : null);

                    RedbackProductInstallationDetail directionDetail = (RedbackProductInstallationDetail) panelInfo[3];
                    panelViewModel.setPanelDirection(SolarPanelsUtility.parseDirection(
                            directionDetail != null ? directionDetail.getValue() : null));

                    RedbackProductInstallationDetail pvSizeDetail = (RedbackProductInstallationDetail) panelInfo[2];
                    panelViewModel.setPvSize(pvSizeDetail != null ?
                            SolarPanelsUtility.parseToDouble(pvSizeDetail.getValue()) : null);

                    return panelViewModel;
                })
                .collect(java.util.stream.Collectors.toList());
    }
}
