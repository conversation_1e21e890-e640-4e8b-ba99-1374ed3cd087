package com.ebon.energy.fms.controller;

import com.ebon.energy.fms.common.annotation.NoLoginRequired;
import com.ebon.energy.fms.common.annotation.OperateLogAnnotation;
import com.ebon.energy.fms.domain.po.*;
import com.ebon.energy.fms.domain.vo.JsonResult;
import com.ebon.energy.fms.domain.vo.PageResult;
import com.ebon.energy.fms.domain.vo.UserListVO;
import com.ebon.energy.fms.service.UserService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

/**
 * 用户管理
 */
@RestController
@RequestMapping("/api/user")
public class UserController {

    @Resource
    private UserService userService;

    /**
     * 登录
     *
     * @param response
     * @param loginPO
     * @return
     */
    @NoLoginRequired
    @OperateLogAnnotation(name="Login")
    @PostMapping("/login")
    public JsonResult login(HttpServletResponse response,
                            @RequestBody @Valid LoginPO loginPO) {
        userService.login(loginPO.getEmail(), loginPO.getPassword(), response);
        return JsonResult.buildSuccess();
    }

    /**
     * 用户登出
     *
     * @param response
     * @return
     */
    @NoLoginRequired
    @RequestMapping(value = "/login-out", method = RequestMethod.GET)
    public JsonResult loginOut(HttpServletResponse response) {
        userService.loginOut(response);
        return JsonResult.buildSuccess();
    }

    /**
     * 新增用户
     *
     * @param createUserPO
     * @return
     */
    @OperateLogAnnotation(name="Add User")
    @PostMapping("/add")
    public JsonResult add(@RequestBody @Valid CreateUserPO createUserPO) {
        userService.createUser(createUserPO);
        return JsonResult.buildSuccess();
    }

    /**
     * 编辑用户
     *
     * @param modifyUserPO
     * @return
     */
    @OperateLogAnnotation(name="Update User")
    @PostMapping("/modify")
    public JsonResult modify(@RequestBody @Valid ModifyUserPO modifyUserPO) {
        userService.modifyUser(modifyUserPO);
        return JsonResult.buildSuccess();
    }

    /**
     * 修改密码
     *
     * @param updatePasswordPO
     * @return
     */
    @OperateLogAnnotation(name="Update Password")
    @PostMapping("/update-password")
    public JsonResult updatePassword(@RequestBody @Valid UpdatePasswordPO updatePasswordPO) {
        userService.updatePassword(updatePasswordPO);
        return JsonResult.buildSuccess();
    }

    /**
     * 重置密码
     * 
     * @param resetPasswordPO 
     * @return
     */
    @OperateLogAnnotation(name="Reset Password")
    @PostMapping("/reset-password")
    public JsonResult resetPassword(@RequestBody @Valid ResetPasswordPO resetPasswordPO) {
        userService.resetPassword(resetPasswordPO);
        return JsonResult.buildSuccess();
    }


    /**
     * 找回密码1：校验邮箱
     *
     * @param findPasswordStepOnePO
     * @return
     */
    @NoLoginRequired
    @PostMapping("/find-password1")
    public JsonResult findPasswordCheckEmail(@RequestBody @Valid FindPasswordStepOnePO findPasswordStepOnePO) {
        userService.findPasswordCheckEmail(findPasswordStepOnePO.getEmail());
        return JsonResult.buildSuccess();
    }

    /**
     * 找回密码2：修改密码
     *
     * @param findPasswordStepTwoPO
     * @return
     */
    @NoLoginRequired
    @PostMapping("/find-password2")
    public JsonResult findPasswordModifyPassword(@RequestBody @Valid FindPasswordStepTwoPO findPasswordStepTwoPO) {
        userService.findPasswordModifyPassword(findPasswordStepTwoPO);
        return JsonResult.buildSuccess();
    }

    /**
     * 用户列表查询
     *
     * @return
     */
    @GetMapping("/list")
    public JsonResult<PageResult<UserListVO>> list(UserListPO userListPO) {
        return JsonResult.buildSuccess(userService.getUserList(userListPO));
    }

}
