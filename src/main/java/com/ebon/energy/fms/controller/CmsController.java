package com.ebon.energy.fms.controller;

import com.ebon.energy.fms.common.annotation.OperateLogAnnotation;
import com.ebon.energy.fms.domain.po.CmsConfigCreatePO;
import com.ebon.energy.fms.domain.po.CmsConfigUpdatePO;
import com.ebon.energy.fms.domain.vo.CmsConfigVO;
import com.ebon.energy.fms.domain.vo.JsonResult;
import com.ebon.energy.fms.domain.vo.PageResult;
import com.ebon.energy.fms.service.CmsConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * cms管理
 */
@Slf4j
@RestController
@RequestMapping("/api/cms")
public class CmsController {

    @Resource
    private CmsConfigService cmsConfigService;

    /**
     * cms配置列表查询
     * 
     * @param current 
     * @param pageSize
     * @return
     */
    @GetMapping("/list")
    public JsonResult<PageResult<CmsConfigVO>> list(@RequestParam(name = "name", required = false) String name,
                                                    @RequestParam(name = "current", defaultValue = "0") Integer current,
                                                    @RequestParam(name = "pageSize", defaultValue = "20") Integer pageSize) {
        return JsonResult.buildSuccess(cmsConfigService.getCmsPage(name, current, pageSize));
    }

    /**
     * cms配置创建
     * 
     * @param po 
     * @return
     */
    @OperateLogAnnotation(name = "Create Cms Config")
    @PostMapping("/create")
    public JsonResult create(@Valid @RequestBody CmsConfigCreatePO po) {
        cmsConfigService.create(po);
        return JsonResult.buildSuccess();
    }

    /**
     * cms配置更新
     * 
     * @param po 
     * @return
     */
    @OperateLogAnnotation(name = "Update Cms Config")
    @PostMapping("/update")
    public JsonResult update(@Valid @RequestBody CmsConfigUpdatePO po) {
        cmsConfigService.update(po);
        return JsonResult.buildSuccess();
    }

}
