package com.ebon.energy.fms.controller;

import com.ebon.energy.fms.common.annotation.OperateLogAnnotation;
import com.ebon.energy.fms.common.enums.ModelTypeEnum;
import com.ebon.energy.fms.domain.po.FirmwareCreatePO;
import com.ebon.energy.fms.domain.po.FirmwareDeletePO;
import com.ebon.energy.fms.domain.po.FirmwareUpdatePO;
import com.ebon.energy.fms.domain.po.FirmwareUpgradePO;
import com.ebon.energy.fms.domain.vo.FirmwareUpdateErrorVO;
import com.ebon.energy.fms.domain.vo.FirmwareVersionDetailVO;
import com.ebon.energy.fms.domain.vo.JsonResult;
import com.ebon.energy.fms.domain.vo.PageResult;
import com.ebon.energy.fms.service.FirmwareService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 固件管理
 */
@RestController
@RequestMapping("/api/firmware")
public class FirmwareController {

    @Resource
    private FirmwareService firmwareService;
    
    /**
     * 版本查询
     *
     * @return List<FirmwareVersionVO>
     */
    @GetMapping("/get-versions")
    public JsonResult<List<FirmwareVersionDetailVO>> getVersions() {
        return JsonResult.buildSuccess(firmwareService.getFirmwareVersion());
    }

    /**
     * 所有Firmware版本查询
     * 
     * @return 
     */
    @GetMapping("/get-all-versions")
    public JsonResult<List<FirmwareVersionDetailVO>> getAllVersions() {
        return JsonResult.buildSuccess(firmwareService.getAllFirmwareVersion());
    }

    /**
     * 根据版本号查询序列号
     * 
     * @param version 
     * @param current
     * @param pageSize
     * @return
     */
    @GetMapping("/get-sn-by-version")
    public JsonResult<PageResult<String>> getSnByVersion(@RequestParam("version") String version,
                                                         @RequestParam(name = "current", defaultValue = "1") Integer current,
                                                         @RequestParam(name = "pageSize", defaultValue = "20") Integer pageSize) {
        return JsonResult.buildSuccess(firmwareService.getSnPageByVersion(ModelTypeEnum.Firmware, version, current, pageSize));
    }

    /**
     * 版本详情查询
     *
     * @return FirmwareVersionVO
     */
    @GetMapping("/get-versions-detail")
    public JsonResult<FirmwareVersionDetailVO> getVersionsDetail(@RequestParam("id") Integer id) {
        return JsonResult.buildSuccess(firmwareService.getFirmwareVersionDetail(id));
    }

    /**
     * 更新固件
     * @param po
     * @return
     */
    @OperateLogAnnotation(name="Upgrade Firmware")
    @PostMapping("/upgrade")
    public JsonResult<List<FirmwareUpdateErrorVO>> upgrade(@RequestBody FirmwareUpgradePO po) {
        return JsonResult.buildSuccess(firmwareService.upgrade(po));
    }

    /**
     * 创建固件版本配置
     * @param po
     * @return
     */
    @OperateLogAnnotation(name="Create Firmware")
    @PostMapping("/create")
    public JsonResult<Void> create(@RequestBody FirmwareCreatePO po) {
        firmwareService.create(po);
        return JsonResult.buildSuccess();
    }


    /**
     * 更新固件版本配置
     * @param po
     * @return
     */
    @OperateLogAnnotation(name="Update Firmware")
    @PostMapping("/update")
    public JsonResult<Void> update(@RequestBody FirmwareUpdatePO po) {
        firmwareService.update(po);
        return JsonResult.buildSuccess();
    }

    /**
     * 删除固件版本配置
     * @param po
     * @return
     */
    @OperateLogAnnotation(name="Delete Firmware")
    @PostMapping("/delete")
    public JsonResult<Void> delete(@RequestBody FirmwareDeletePO po) {
        firmwareService.delete(po);
        return JsonResult.buildSuccess();
    }

}
