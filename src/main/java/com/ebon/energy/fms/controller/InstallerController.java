package com.ebon.energy.fms.controller;

import com.ebon.energy.fms.domain.vo.InstallerCompanyVO;
import com.ebon.energy.fms.domain.vo.JsonResult;
import com.ebon.energy.fms.service.InstallerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 安装管理
 */
@Slf4j
@RestController
@RequestMapping("/api/installer")
public class InstallerController {

    @Resource
    private InstallerService installerService;

    /**
     * 安装公司列表
     * 
     * @return 
     */
    @GetMapping("/companys")
    public JsonResult<List<InstallerCompanyVO>> installerCompanys() {
        return JsonResult.buildSuccess(installerService.getInstallerCompanys());
    }
}
