package com.ebon.energy.fms.controller;

import com.azure.core.annotation.Get;
import com.ebon.energy.fms.common.annotation.OperateLogAnnotation;
import com.ebon.energy.fms.common.enums.BatteryModelEnum;
import com.ebon.energy.fms.common.enums.UniversalSettingId;
import com.ebon.energy.fms.common.utils.MenuItem;
import com.ebon.energy.fms.controller.request.ApllyBatterySettingRequest;
import com.ebon.energy.fms.domain.vo.ElectricalConfiguration;
import com.ebon.energy.fms.domain.vo.ElectricalViewModel;
import com.ebon.energy.fms.domain.vo.JsonResult;
import com.ebon.energy.fms.domain.vo.setting.BatteryModelResponse;
import com.ebon.energy.fms.domain.vo.setting.UniversalSettingValueDto;
import com.ebon.energy.fms.domain.vo.setting.modular.*;
import com.ebon.energy.fms.service.SettingsService;
import com.ebon.energy.fms.service.setting.BatterySettingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.ebon.energy.fms.common.enums.BatteryModelEnum.*;

/**
 * 逆变器settings管理
 */
@Slf4j
@RestController
@RequestMapping("/api/settings")
public class SettingsController {

    @Resource
    private SettingsService settingsService;

    @Resource
    private BatterySettingService batterySettingService;

    /**
     * 逆变器settings查询
     *
     * @param sn 序列号
     * @return
     */
    @GetMapping("/get")
    public JsonResult getSettings(String sn) {
        return JsonResult.buildSuccess(settingsService.getSettings(sn));
    }

    @GetMapping("/battery")
    public JsonResult<Map<UniversalSettingId, UniversalSettingValueDto>> getSettingsBattert(String sn) {
        return settingsService.getSettingsBattery(sn);
    }

    @OperateLogAnnotation(name = "Apply Battery Settings")
    @PostMapping("/battery/apply")
    public JsonResult<Void> applySettingsBattert(@RequestBody ApllyBatterySettingRequest request) {
        log.info("applySettingsBattert:{}", request);
        settingsService.applySettingsBattery(request.getSn(), request);
        return JsonResult.buildSuccess(null);
    }


    @GetMapping("/ross1/battery")
    public JsonResult<ElectricalViewModel> getRoss1SettingsBattery(String sn) {
        return JsonResult.buildSuccess(batterySettingService.getRoss1SettingsBattery(sn));
    }

    @OperateLogAnnotation(name = "Apply Ross1 Battry Settings")
    @PostMapping("/ross1/battery/apply")
    public JsonResult<Void> applyRoss1SettingsBattery(@RequestBody ElectricalViewModel request) {
        log.info("applyRoss1SettingsBattery:{}", request);
        batterySettingService.applyRoss1SettingsBattery(request);
        return JsonResult.buildSuccess(null);
    }


    /**
     *  获取电池模型型号
     * @param manufacturer
     * @return
     */
    @GetMapping("/getBatteryModels")
    public JsonResult<List<BatteryModelResponse>> getBatteryModels(@RequestParam String manufacturer) {
        var manufacturerEnum = ElectricalConfiguration.BatteryManufacturer.valueOf(manufacturer);
        var modelsMap = MenuItem.getBatteryModelMaps(manufacturerEnum);
        var collect = modelsMap.entrySet().stream().map(entry -> BatteryModelResponse
                .builder().model(entry.getKey().name()).value(entry.getValue()).build()).collect(Collectors.toList());
        return  JsonResult.buildSuccess(collect);
    }

    /**
     * 计算电池默认设置
     *
     * @param model    电池型号（字符串，枚举名）
     * @param units    模块/并联数量（Modular/BMS 电池用）
     * @param capacity 容量(Ah)（Lead-Acid 用）
     */
    @GetMapping("/battery/calculated-settings")
    public JsonResult<?> getCalculatedBatterySettings(@RequestParam String model,
                                                      @RequestParam(required = false, defaultValue = "0") int units,
                                                      @RequestParam(required = false, defaultValue = "0") int capacity) {
        try {
            Object batteryDefault = null;

            // 枚举校验
            BatteryModelEnum modelEnum;
            try {
                modelEnum = BatteryModelEnum.valueOf(model);
            } catch (IllegalArgumentException ex) {
                return JsonResult.buildError("非法的电池型号: " + model);
            }

            // 根据型号实例化对应 Default
            if (modelEnum == Aquion_S20P) {
                batteryDefault = new ModularBatteryDefault(new AquionS20PDefault(), units);
            } else if (modelEnum == Aquion_S30) {
                batteryDefault = new ModularBatteryDefault(new AquionS30Default(), units);
            } else if (modelEnum == Aquion_Aspen_48S_2_2) {
                batteryDefault = new ModularBatteryDefault(new AquionAspen48S2_2Default(), units);
            } else if (modelEnum == SimpliPHi_2_6) {
                batteryDefault = new ModularBatteryDefault(new SimpliPHi_2_6Default(), units);
            } else if (modelEnum == SimpliPHi_3_4) {
                batteryDefault = new ModularBatteryDefault(new SimpliPHi_3_4Default(), units);
            } else if (modelEnum == LeadAcid_Flooded) {
                batteryDefault = new CellularBatteryDefault(new LeadAcidFloodedDefault(), capacity);
            } else if (modelEnum == LeadAcid_Sealed) {
                batteryDefault = new CellularBatteryDefault(new LeadAcidSealedDefault(), capacity);
            } else if (modelEnum == LG_M48063P3S) {
                batteryDefault = new BMSBatteryDefault(new LGM48063P3SDefault(), units);
            } else if (modelEnum == LG_M48126P3S) {
                batteryDefault = new BMSBatteryDefault(new LGM48126P3SDefault(), units);
            } else if (modelEnum == LG_RESU_Plus) {
                batteryDefault = new BMSBatteryDefault(new LGRESUPlusDefault(), capacity);
            } else if (modelEnum == LG_RESU_3_3) {
                batteryDefault = new BMSBatteryDefault(new LGRESU3_3Default(), capacity);
            } else if (modelEnum == LG_RESU_6_4Ex) {
                batteryDefault = new BMSBatteryDefault(new LGRESU6_4Default(), capacity);
            } else if (modelEnum == LG_RESU_6_5) {
                batteryDefault = new BMSBatteryDefault(new LGRESU6_5Default(), capacity);
            } else if (modelEnum == LG_RESU_10) {
                batteryDefault = new BMSBatteryDefault(new LGRESU10Default(), capacity);
            } else {
                /* 其它型号暂不处理 */
            }


            // 参数校验
            if (batteryDefault instanceof ModularBatteryDefault) {
                if (units <= 0 || units >= 100) {
                    return JsonResult.buildSuccess(Map.of("Result",
                            "Number of battery modules must be between 1 and 99."));
                }
            } else if (batteryDefault instanceof CellularBatteryDefault) {
                if (capacity <= 0 || capacity > 2000) {
                    return JsonResult.buildSuccess(Map.of("Result",
                            "Capacity (Ah) must be between 1 and 2000 for Lead Acid battery types."));
                }
            }

            // 正常返回
            if (batteryDefault != null) {
                if (batteryDefault instanceof ModularBatteryDefault) {
                    return JsonResult.buildSuccess(Map.of("Result", "Modular", "Default", batteryDefault));
                } else if (batteryDefault instanceof CellularBatteryDefault) {
                    return JsonResult.buildSuccess(Map.of("Result", "Cellular", "Default", batteryDefault));
                } else if (batteryDefault instanceof BMSBatteryDefault) {
                    return JsonResult.buildSuccess(Map.of("Result", "BMS", "Default", batteryDefault));
                }
            }
            return JsonResult.buildSuccess(Map.of("Result", "Not Valid Type"));
        } catch (Exception ex) {
            log.error("getCalculatedBatterySettings error", ex);
            return JsonResult.buildError("Error when getting installer setting status update. Please re-try. Error: "
                    + ex.getMessage());
        }
    }





}
