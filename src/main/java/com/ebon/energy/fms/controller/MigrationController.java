package com.ebon.energy.fms.controller;

import com.ebon.energy.fms.domain.po.InvertersListPO;
import com.ebon.energy.fms.domain.po.MigrationTwinsPO;
import com.ebon.energy.fms.domain.vo.JsonResult;
import com.ebon.energy.fms.service.MigrationService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * Twins Desired迁移工具
 */
@RestController
@RequestMapping("/api/migration")
public class MigrationController {

    @Resource
    private MigrationService migrationService;

    @GetMapping("/get-azure-twins")
    public JsonResult<String> getAzureTwins(@RequestParam("serialNumber") String serialNumber,
                                   @RequestParam("applicationName") String applicationName) throws Exception {
        String string = migrationService.get(serialNumber, applicationName);
        return JsonResult.buildSuccess(string);
    }

    @PostMapping("/get-azure-twins-batch")
    public JsonResult<Map<String, Object>> getAzureTwinsBatch(@RequestBody MigrationTwinsPO po) throws Exception {
        return JsonResult.buildSuccess(migrationService.getBatch(po.getSerialNumbers(), po.getApplicationName()));
    }


    /**
     * 执行迁移
     *
     * @param serialNumbers
     * @param applicationName
     * @return
     */
    @PostMapping("/migration-twins-to-tuya")
    public JsonResult<String> migration(@RequestBody MigrationTwinsPO po) throws Exception {
        migrationService.migrationBatch(po.getSerialNumbers(), po.getApplicationName());
        return JsonResult.buildSuccess();
    }
}
