package com.ebon.energy.fms.controller;

import com.ebon.energy.fms.common.annotation.OperateLogAnnotation;
import com.ebon.energy.fms.common.enums.ModelTypeEnum;
import com.ebon.energy.fms.domain.entity.InvertersDO;
import com.ebon.energy.fms.domain.po.*;
import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.service.GridTieFirmwareVersionsService;
import com.ebon.energy.fms.service.InverterService;
import com.ebon.energy.fms.service.ModelVersionRelationService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.ebon.energy.fms.util.StreamUtil.mapList;


@RestController
@RequestMapping("/api/grid-tie-firmware")
public class GridTieFirmwareController {

    @Resource
    private GridTieFirmwareVersionsService gridTieFirmwareVersionsService;

    @Resource
    private ModelVersionRelationService modelVersionRelationService;
    
    @Resource 
    private InverterService inverterService;

    /**
     * 版本查询
     *
     * @return List<GridTieFirmwareVersionsVO>
     */
    @GetMapping("/get-versions")
    public JsonResult<List<GridTieFirmwareVersionsVO>> getVersions() {
        List<GridTieFirmwareVersionsVO> list = gridTieFirmwareVersionsService.getList(true);
        return JsonResult.buildSuccess(list);
    }

    /**
     * 所有EMS版本查询
     *
     * @return
     */
    @GetMapping("/get-all-versions")
    public JsonResult<List<GridTieFirmwareVersionsVO>> getAllVersions() {
        return JsonResult.buildSuccess(gridTieFirmwareVersionsService.getList(null));
    }

    /**
     * 根据版本号查询序列号
     *
     * @param version
     * @param current
     * @param pageSize
     * @return
     */
    @GetMapping("/get-sn-by-version")
    public JsonResult<PageResult<String>> getSnByVersion(@RequestParam("version") String version,
                                                         @RequestParam(name = "current", defaultValue = "1") Integer current,
                                                         @RequestParam(name = "pageSize", defaultValue = "20") Integer pageSize) {
        return JsonResult.buildSuccess(inverterService.getSnPageByVersion(ModelTypeEnum.EMSFirmware, version, current, pageSize));
    }

    /**
     * 版本详情查询
     *
     * @return
     */
    @GetMapping("/get-version-detail")
    public JsonResult<Map<String, Object>> getVersionDetail(@RequestParam("id") String id) {
        GridTieFirmwareVersionsVO detail = gridTieFirmwareVersionsService.getVersionDetail(id);
        Map<String, Object> map = new HashMap<>();
        map.put("officialVersions", detail);
        map.put("storageContainerName", gridTieFirmwareVersionsService.getStorageContainerURL());
        return JsonResult.buildSuccess(map);
    }

    /**
     * 更新(批量)
     *
     * @param po po
     * @return
     */
    @OperateLogAnnotation(name="Update EMS Firmware")
    @PostMapping("/update")
    public JsonResult<List<GridTieFirmwareVersionsUpdateErrorVO>> update(@Valid @RequestBody GridTieFirmwareVersionsUpdatePO po) throws Exception {
        return JsonResult.buildSuccess(gridTieFirmwareVersionsService.update(po));
    }


    /**
     * 根据模型ID获取精简版本号列表
     * @param modelId 模型ID
     * @return 版本号列表
     */
    @GetMapping("/get-model-versions")
    public JsonResult<List<String>> getVersionStringsByModelId(
            @RequestParam("modelId") Integer modelId) {
        List<String> versions = modelVersionRelationService.listVersionsByModelId(modelId, ModelTypeEnum.EMSFirmware);
        return JsonResult.buildSuccess(versions);
    }


    /**
     * 根据模型Name获取精简版本号列表
     * @param modelName 模型Name
     * @param type 类型 默认0 0EMSFirmware 1Firmware
     * @return 版本号列表
     */
    @GetMapping("/get-versions-by-model-name")
    public JsonResult<Map<String, List<String>>> getVersionStringsByModelName(
            @RequestParam("modelNames") List<String> modelNames,
            @RequestParam(value = "type", defaultValue = "0") Integer modelType) {
        Map<String, List<String>> versions = modelVersionRelationService.listVersionsByModelName(modelNames, ModelTypeEnum.fromValue(modelType));
        return JsonResult.buildSuccess(versions);
    }


    /**
     * 更新ems版本配置
     *
     * @param po po
     * @return
     */
    @OperateLogAnnotation(name="Edit EMS Firmware Version")
    @PostMapping("/edit")
    public JsonResult<Void> edit(@Valid @RequestBody GridTieFirmwareVersionsEditPO po) throws Exception {
        gridTieFirmwareVersionsService.edit(po);
        return JsonResult.buildSuccess();
    }


    /**
     * 新增ems版本配置
     *
     * @param po po
     * @return
     */
    @OperateLogAnnotation(name="Create EMS Firmware Version")
    @PostMapping("/create")
    public JsonResult<Void> create(@Valid @RequestBody GridTieFirmwareVersionsCreatePO po) throws Exception {
        gridTieFirmwareVersionsService.create(po);
        return JsonResult.buildSuccess();
    }

    /**
     * 删除ems版本配置
     * @param po
     * @return
     */
    @OperateLogAnnotation(name="Delete EMS Firmware")
    @PostMapping("/delete")
    public JsonResult<Void> delete(@RequestBody FirmwareDeletePO po) {
        gridTieFirmwareVersionsService.delete(po);
        return JsonResult.buildSuccess();
    }
}
