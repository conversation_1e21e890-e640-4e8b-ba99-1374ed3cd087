package com.ebon.energy.fms.controller;

import com.azure.core.annotation.Get;
import com.ebon.energy.fms.domain.vo.JsonResult;
import com.ebon.energy.fms.domain.vo.installer.FleetHealthSummaryViewModel;
import com.ebon.energy.fms.domain.vo.installer.InstallationSummaryViewModel;
import com.ebon.energy.fms.service.installer.InstallationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/largeinstaller")
public class LargeInstallerController {

    private final InstallationService installationService;

    @GetMapping(value = {"/fleet-health-summary/{installerId}","/fleet-health-summary"})
    public JsonResult<List<FleetHealthSummaryViewModel>> fleetHealthSummary(@PathVariable(required = false) String installerId) {
        return JsonResult.buildSuccess(installationService.getFleetHealthSummaryAsync(installerId));
    }


    @GetMapping(value = {"/installation-summary/{installerId}", "/installation-summary"})
    public JsonResult<InstallationSummaryViewModel> installationSummary(@PathVariable(required = false) String installerId) {
        return JsonResult.buildSuccess(installationService.installationSummary(installerId));
    }
}
