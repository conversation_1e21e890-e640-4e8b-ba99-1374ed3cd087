package com.ebon.energy.fms.controller;

import com.ebon.energy.fms.common.annotation.OperateLogAnnotation;
import com.ebon.energy.fms.domain.po.CreatePushTaskPO;
import com.ebon.energy.fms.domain.po.PushTaskQueryPO;
import com.ebon.energy.fms.domain.po.UpdatePushTaskPO;
import com.ebon.energy.fms.domain.vo.AppVersionManagementVO;
import com.ebon.energy.fms.domain.vo.JsonResult;
import com.ebon.energy.fms.domain.vo.PageResult;
import com.ebon.energy.fms.domain.vo.PushTaskVO;
import com.ebon.energy.fms.domain.vo.PushTaskStatisticsVO;
import com.ebon.energy.fms.service.AppVersionManagementService;
import com.ebon.energy.fms.service.PushTaskService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 推送任务控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/push")
@RequiredArgsConstructor
public class PushTaskController {

    private final PushTaskService pushTaskService;
    private final AppVersionManagementService appVersionManagementService;

    /**
     * 创建推送任务
     *
     * @param po 创建请求
     * @return 任务ID
     */
    @PostMapping("/create")
    @OperateLogAnnotation(name = "Create Push Task")
    public JsonResult<Long> createPushTask(@Valid @RequestBody CreatePushTaskPO po) {
        log.info("创建推送任务请求: {}", po);
        Long taskId = pushTaskService.createPushTask(po);
        return JsonResult.buildSuccess(taskId);
    }

    /**
     * 分页查询推送任务
     *
     * @param current 当前页码
     * @param pageSize 每页大小
     * @param os 操作系统过滤
     * @param status 状态过滤
     * @param keyword 关键词搜索
     * @return 分页结果
     */
    @GetMapping("/page")
    public JsonResult<PageResult<PushTaskVO>> queryPushTasks(
            @RequestParam(name = "current", defaultValue = "1") Integer current,
            @RequestParam(name = "pageSize", defaultValue = "20") Integer pageSize,
            @RequestParam(name = "os", required = false) String os,
            @RequestParam(name = "status", required = false) String status,
            @RequestParam(name = "keyword", required = false) String keyword) {
        
        PushTaskQueryPO po = new PushTaskQueryPO();
        po.setCurrent(current);
        po.setPageSize(pageSize);
        po.setOs(os);
        po.setStatus(status);
        po.setKeyword(keyword);
        
        log.info("查询推送任务请求: {}", po);
        PageResult<PushTaskVO> result = pushTaskService.queryPushTasks(po);
        return JsonResult.buildSuccess(result);
    }

    /**
     * 根据ID查询推送任务详情
     *
     * @param id 任务ID
     * @return 任务详情
     */
    @GetMapping("/detail/{id}")
    public JsonResult<PushTaskVO> getPushTaskById(@PathVariable Long id) {
        log.info("查询推送任务详情，ID: {}", id);
        PushTaskVO task = pushTaskService.getPushTaskById(id);
        return JsonResult.buildSuccess(task);
    }

    /**
     * 更新推送任务（仅限PENDING状态）
     *
     * @param id 任务ID
     * @param po 更新请求
     * @return 成功响应
     */
    @OperateLogAnnotation(name = "Update Push Task")
    @PostMapping("/update")
    public JsonResult<Void> updatePushTask(@Valid @RequestBody UpdatePushTaskPO po) {
        log.info("更新推送任务请求， 参数: {}",  po);
        pushTaskService.updatePushTask(po);
        return JsonResult.buildSuccess();
    }

    /**
     * 获取推送任务统计信息
     *
     * @return 统计信息
     */
    @GetMapping("/statistics")
    public JsonResult<PushTaskStatisticsVO> getStatistics() {
        log.info("查询推送任务统计信息");
        PushTaskStatisticsVO statistics = pushTaskService.getStatistics();
        return JsonResult.buildSuccess(statistics);
    }

    /**
     * 获取所有App版本列表（不分页）
     *
     * @param keyword 关键词搜索
     * @return App版本列表
     */
    @GetMapping("/app-versions")
    public JsonResult<List<AppVersionManagementVO>> getAllAppVersions(
            @RequestParam(name = "keyword", required = false) String keyword) {
        log.info("查询所有App版本列表，关键词: {}", keyword);
        List<AppVersionManagementVO> appVersions = appVersionManagementService.getAllAppVersions(keyword);
        return JsonResult.buildSuccess(appVersions);
    }
}
