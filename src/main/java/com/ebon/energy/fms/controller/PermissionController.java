package com.ebon.energy.fms.controller;

import com.ebon.energy.fms.common.annotation.OperateLogAnnotation;
import com.ebon.energy.fms.domain.po.RoleListPO;
import com.ebon.energy.fms.domain.po.RoleModifyPO;
import com.ebon.energy.fms.domain.po.RolePO;
import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.service.PermissionService;
import com.ebon.energy.fms.service.RoleService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 权限管理
 */
@RestController
@RequestMapping("/api/permission")
public class PermissionController {

    @Resource
    private PermissionService permissionService;

    /**
     * 权限列表查询
     *
     * @return
     */
    @GetMapping("/list")
    public JsonResult<List<PermissionVO>> list() {
        return JsonResult.buildSuccess(permissionService.getPermissionTree());
    }

    /**
     * 当前用户权限code列表
     *
     * @return
     */
    @GetMapping("/current-codes")
    public JsonResult<List<String>> codes() {
        return JsonResult.buildSuccess(permissionService.getCurrentUserPermCodes());
    }

}
