package com.ebon.energy.fms.controller;

import com.ebon.energy.fms.common.enums.SGErrorCode;
import com.ebon.energy.fms.domain.po.ErrorStatisticsQueryPO;
import com.ebon.energy.fms.domain.vo.ErrorMappingVO;
import com.ebon.energy.fms.domain.vo.InverterErrorStatisticsVO;
import com.ebon.energy.fms.domain.vo.JsonResult;
import com.ebon.energy.fms.domain.vo.PageResult;
import com.ebon.energy.fms.service.ErrorService;
import com.ebon.energy.fms.service.InverterErrorStatisticsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import static com.ebon.energy.fms.util.StreamUtil.mapList;

/**
 * 逆变器异常统计
 */
@Slf4j
@RestController
@RequestMapping("/api/error-statistics")
public class InverterErrorStatisticsController {

    @Resource
    private InverterErrorStatisticsService inverterErrorStatisticsService;

    @Resource
    private ErrorService errorService;

    /**
     * 逆变器异常统计查询
     *
     * @param queryPO
     * @return
     */
    @GetMapping("/list")
    public JsonResult<PageResult<InverterErrorStatisticsVO>> list(ErrorStatisticsQueryPO queryPO) {
        return JsonResult.buildSuccess(inverterErrorStatisticsService.getErrorStatisticsPage(queryPO));
    }

    /**
     * 错误枚举列表
     *
     * @return
     */
    @GetMapping("/errors")
    public JsonResult<List<String>> errors() {
        List<ErrorMappingVO> errorMappings = errorService.getErrorMappings();
        List<String> errors = mapList(errorMappings, ErrorMappingVO::getErrorCodeParsed);
        errors.addAll(Arrays.stream(SGErrorCode.values()).filter(e -> !e.equals(SGErrorCode.NoError)).map(e -> e.name()).collect(Collectors.toList()));
        return JsonResult.buildSuccess(errors);
    }

}
