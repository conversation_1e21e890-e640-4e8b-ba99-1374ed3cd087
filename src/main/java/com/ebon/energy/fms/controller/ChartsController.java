package com.ebon.energy.fms.controller;


import com.ebon.energy.fms.controller.request.ChartsPlotOptionsRequest;
import com.ebon.energy.fms.controller.request.ChartsSitePlotOptionsRequest;
import com.ebon.energy.fms.domain.vo.JsonResult;
import com.ebon.energy.fms.domain.vo.charts.ChartsDataBandVO;
import com.ebon.energy.fms.domain.vo.charts.ChartsViewModel;
import com.ebon.energy.fms.service.charts.ChartsService;
import com.ebon.energy.fms.service.site.SiteChartsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * Charts 图表接口
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/charts")
public class ChartsController {

    private final ChartsService chartsService;

    @GetMapping("/{sn}")
    public JsonResult<ChartsViewModel> getCharts(@PathVariable("sn") String sn) {
        return JsonResult.buildSuccess(chartsService.getCharts(sn));
    }

    @GetMapping("/data-band")
    public JsonResult<ChartsDataBandVO> dataBand(ChartsPlotOptionsRequest chartsPlotOptionsRequest) {
        return JsonResult.buildSuccess(chartsService.dataBand(chartsPlotOptionsRequest));
    }


}
