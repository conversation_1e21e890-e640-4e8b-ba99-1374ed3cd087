package com.ebon.energy.fms.controller;

import com.ebon.energy.fms.domain.vo.JsonResult;
import com.ebon.energy.fms.domain.vo.PageResult;
import com.ebon.energy.fms.domain.vo.alert.AlertDetails;
import com.ebon.energy.fms.domain.vo.alert.AlertModel;
import com.ebon.energy.fms.domain.vo.alert.SeverityCategoryModel;
import com.ebon.energy.fms.service.AlertService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 告警管理控制器
 * 从C# AlertsController转换而来
 */
@Slf4j
@RestController
@RequestMapping("/api/alerts")
public class AlertsController {

    @Resource
    private AlertService alertService;

    /**
     * 根据时间段获取告警汇总信息
     * GET: /api/alerts/summary
     */
    @GetMapping("/summary")
    public JsonResult<SeverityCategoryModel> getAlertSummaryByPeriod(@RequestParam(required = false) Integer timeRange) {
        try {
            SeverityCategoryModel severitySummary = alertService.getAlertSummaryByPeriod(timeRange);
            return JsonResult.buildSuccess(severitySummary);

        } catch (Exception e) {
            log.error("获取告警汇总信息失败", e);
            return JsonResult.buildError("获取告警汇总信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取过滤后的告警数据（分页）
     * GET: /api/alerts/data
     */
    @GetMapping("/data")
    public JsonResult<PageResult<AlertModel>> getAlertData(
            @RequestParam int timeRange,
            @RequestParam String sev,
            @RequestParam String monitoringConditions,
            @RequestParam String states,
            @RequestParam(required = false) String monitorConditionType,
            @RequestParam(defaultValue = "1") long current,
            @RequestParam(defaultValue = "20") long pageSize) {

        try {
            PageResult<AlertModel> pageResult = alertService.getAlertData(
                timeRange,
                sev,
                monitoringConditions,
                states,
                monitorConditionType,
                current,
                pageSize
            );

            return JsonResult.buildSuccess(pageResult);

        } catch (Exception e) {
            log.error("获取告警数据失败", e);
            return JsonResult.buildError("获取告警数据失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID获取告警详情
     * GET: /api/alerts/{alertId}
     */
    @GetMapping("/query")
    public JsonResult<AlertDetails> getAlertDataById(@RequestParam String alertId) {
        try {
            AlertDetails alertDetails = alertService.getAlertDataById(alertId);

            if (alertDetails == null || alertDetails.getDetail() == null) {
                return JsonResult.buildError("未找到指定的告警信息");
            }

            return JsonResult.buildSuccess(alertDetails);

        } catch (Exception e) {
            log.error("获取告警详情失败, alertId: {}", alertId, e);
            return JsonResult.buildError("获取告警详情失败: " + e.getMessage());
        }
    }
}
