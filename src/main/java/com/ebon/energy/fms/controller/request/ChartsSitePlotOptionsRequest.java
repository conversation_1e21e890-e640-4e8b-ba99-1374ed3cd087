package com.ebon.energy.fms.controller.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * Site charts plot options request
 */
@Data
public class ChartsSitePlotOptionsRequest {
    
    @NotBlank(message = "Site ID is required")
    @JsonProperty("SiteId")
    private String siteId;

    @NotBlank(message = "Start date is required")
    @JsonProperty("StartDate")
    private String startDate;

    @NotBlank(message = "End date is required")
    @JsonProperty("EndDate")
    private String endDate;

    @JsonProperty("TimeZone")
    @NotBlank(message = "Time zone is required")
    private String TimeZone;
}