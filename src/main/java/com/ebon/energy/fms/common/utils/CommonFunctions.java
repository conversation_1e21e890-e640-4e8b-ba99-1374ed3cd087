package com.ebon.energy.fms.common.utils;

import com.ebon.energy.fms.common.enums.HardwareModelEnum;
import com.ebon.energy.fms.domain.vo.product.control.invert.DesiredAndReported;
import org.apache.commons.lang3.StringUtils;

/**
 * 通用功能工具类
 * 提供硬件模型解析等通用功能
 */
public class CommonFunctions {

    /**
     * 获取硬件模型
     * 基于序列号、设置信息和模型名称确定硬件模型
     * 
     * @param serialNumber 序列号
     * @param desiredAndReported 期望和报告的设置
     * @param modelName 模型名称
     * @return 硬件模型枚举
     */
    public static HardwareModelEnum getHardwareModel(
            String serialNumber, 
            DesiredAndReported desiredAndReported, 
            String modelName) {
        
        // 首先尝试从模型名称解析
        if (StringUtils.isNotBlank(modelName)) {
            HardwareModelEnum modelFromName = HardwareModelHelpers.parseModelName(modelName);
            if (modelFromName != HardwareModelEnum.Unknown) {
                return modelFromName;
            }
        }
        
        // 如果模型名称解析失败，尝试从序列号解析
        if (StringUtils.isNotBlank(serialNumber)) {
            HardwareModelEnum modelFromSerial = HardwareModelHelpers.determineFromSerialNumber(serialNumber);
            if (modelFromSerial != HardwareModelEnum.Unknown) {
                return modelFromSerial;
            }
        }
        
        // 如果都失败了，返回Unknown
        return HardwareModelEnum.Unknown;
    }
    
    /**
     * 检查字符串是否为空或null
     * 
     * @param str 要检查的字符串
     * @return 如果字符串为null或空则返回true
     */
    public static boolean isNullOrEmpty(String str) {
        return str == null || str.trim().isEmpty();
    }
    
    /**
     * 安全地从固件版本字符串中提取ARM版本
     * 
     * @param firmwareVersion 固件版本字符串
     * @return ARM版本号，如果解析失败返回0
     */
    public static int extractArmVersionSafely(String firmwareVersion) {
        if (isNullOrEmpty(firmwareVersion)) {
            return 0;
        }
        
        try {
            return ArmAndDspVersions.getOrZeros(firmwareVersion).getArmVersion();
        } catch (Exception e) {
            return 0;
        }
    }
}
