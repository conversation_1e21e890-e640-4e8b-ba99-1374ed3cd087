// Copyright (c) Redback Technologies. All Rights Reserved.

package com.ebon.energy.fms.common.utils;

import com.ebon.energy.fms.common.enums.AustralianState;

import java.util.regex.Pattern;

/**
 * State Validator
 */
public class StateValidator {

    private static final Pattern NON_ALPHA_PATTERN = Pattern.compile("[^a-zA-Z]");

    /**
     * Try parse
     * @param input input string
     * @param result output parameter array (Java equivalent of C# out parameter)
     * @return true if parsing succeeded, false otherwise
     */
    public static boolean tryParse(String input, AustralianState[] result) {
        String regex = "[^a-zA-Z]";
        String cleanedValue = NON_ALPHA_PATTERN.matcher(input != null ? input : "")
                .replaceAll("")
                .toUpperCase();

        try {
            AustralianState state = AustralianState.valueOf(cleanedValue);
            if (result != null && result.length > 0) {
                result[0] = state;
            }
            return true;
        } catch (IllegalArgumentException e) {
            if (result != null && result.length > 0) {
                result[0] = AustralianState.Unknown;
            }
            return false;
        }
    }

    /**
     * Try parse (simplified version)
     * @param input input string
     * @return parsed AustralianState or Unknown if parsing failed
     */
    public static AustralianState tryParse(String input) {
        AustralianState[] result = new AustralianState[1];
        tryParse(input, result);
        return result[0];
    }

    /**
     * Validate if state string is valid
     * @param input state string
     * @return true if valid Australian state, false otherwise
     */
    public static boolean isValidState(String input) {
        AustralianState[] result = new AustralianState[1];
        return tryParse(input, result) && result[0] != AustralianState.Unknown;
    }
}
