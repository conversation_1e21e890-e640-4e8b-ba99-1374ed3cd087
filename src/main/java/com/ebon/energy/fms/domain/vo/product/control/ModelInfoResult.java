package com.ebon.energy.fms.domain.vo.product.control;

import com.ebon.energy.fms.common.utils.ModelInfo;
import com.ebon.energy.fms.domain.vo.product.control.invert.DesiredAndReported;
import lombok.Data;
import lombok.AllArgsConstructor;

/**
 * 模型信息结果类
 * 包装ModelInfo和DesiredAndReported的返回结果
 */
@Data
@AllArgsConstructor
public class ModelInfoResult {
    
    /**
     * 模型信息
     */
    private ModelInfo model;
    
    /**
     * 期望和报告的设置
     */
    private DesiredAndReported settings;
}
