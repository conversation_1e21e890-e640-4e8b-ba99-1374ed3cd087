package com.ebon.energy.fms.domain.vo.product.control;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

/**
 * 内部模型信息类，用于数据库查询结果映射
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class InternalModelInfo {
    
    @JsonProperty("ModelName")
    private String modelName;
    
    @JsonProperty("FirmwareVersion")
    private String firmwareVersion;
    
    @JsonProperty("Desired")
    private String desired;
    
    @JsonProperty("Reported")
    private String reported;
}
