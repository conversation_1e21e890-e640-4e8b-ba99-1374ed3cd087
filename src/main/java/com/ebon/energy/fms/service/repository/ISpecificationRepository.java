package com.ebon.energy.fms.service.repository;


import com.ebon.energy.fms.domain.vo.product.control.HardwareFirmwareSpecification;
import com.ebon.energy.fms.domain.vo.product.control.InstallationSpecification;
import com.ebon.energy.fms.domain.vo.product.control.ModelInfoResult;

/**
 * 规格仓储接口
 * 定义产品规格相关的数据访问方法
 */
public interface ISpecificationRepository {

    /**
     * 获取模型信息
     * @param userId 用户ID
     * @param serialNumber 序列号
     * @return 模型信息结果
     */
    ModelInfoResult getModelInfoAsync(String userId, String serialNumber);

    /**
     * 获取产品规格
     * @param userId 用户ID
     * @param serialNumber 序列号
     * @return 产品默认配置
     */
    HardwareFirmwareSpecification getSpecAsync(String userId, String serialNumber);

    /**
     * 获取安装规格
     * @param userId 用户ID
     * @param serialNumber 序列号
     * @return 安装规格
     */
    InstallationSpecification getInstallationSpecAsync(String userId, String serialNumber);
}
