# FMS Server Development Guide

## Build/Test/Lint Commands
- **Build**: `mvn clean compile` 
- **Test all**: `mvn test`
- **Test single**: `mvn test -Dtest=InverterTest#testInverter`
- **Package**: `mvn clean package`
- **Run app**: `mvn spring-boot:run`

## Code Style & Conventions
- **Framework**: Spring Boot 2.7.18, Java 11, Maven
- **Dependency Injection**: Use `@Resource` for field injection (not constructor)
- **Annotations**: Controllers use `@RestController`, services use `@Service`
- **Naming**: Classes end with suffix (Controller, Service, PO, VO, DO)
- **Response Pattern**: All API methods return `JsonResult<T>` wrapper
- **Validation**: Use `@Valid` with request bodies, validation annotations on PO classes
- **Imports**: Standard Java imports, Spring framework, Lombok for boilerplate
- **Error Handling**: Use `BizException` for business logic errors
- **Logging**: Use `@Slf4j` annotation and log.error() for errors
- **Custom Annotations**: `@NoLoginRequired` for public endpoints, `@OperateLogAnnotation` for audit
- **URL Patterns**: REST endpoints use kebab-case (/api/user/login-out)

## Project Structure
- Domain objects: PO (Parameter Objects), VO (Value Objects), DO (Data Objects)
- Service layer uses factory pattern for multi-implementation services
- Controllers in `/controller/`, services in `/service/`, mappers for data access
