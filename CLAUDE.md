# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Repository Context

This FMS Server is part of the larger Redback Technologies energy management ecosystem located at `/Users/<USER>/project/ebon/redabck/`. The ecosystem includes:

1. **fms-server** (this project) - Java/Spring Boot microservice for Fleet Management System
2. **Platform** - Main .NET-based IoT platform handling device telemetry, web portals, and mobile apps

Both projects work together to provide comprehensive energy management solutions.

## Commands

### Build and Run
- **Compile**: `mvn clean compile`
- **Run application**: `mvn spring-boot:run`
- **Package JAR**: `mvn clean package`
- **Package without tests**: `mvn clean package -DskipTests`

### Testing
- **Run all tests**: `mvn test`
- **Run single test class**: `mvn test -Dtest=TestClassName`
- **Run single test method**: `mvn test -Dtest=TestClassName#methodName`

## Architecture Overview

This is a Spring Boot 2.7.18 application (Java 11) implementing a Fleet Management System (FMS) for energy/inverter management. The application follows a layered architecture with clear separation of concerns.

### Key Architectural Patterns

1. **Multi-datasource Configuration**: The application uses three separate datasources (primary, second, third) with corresponding mapper packages
2. **Factory Pattern**: Service layer uses factory pattern for multi-implementation services (e.g., `AbstractInverterService` with implementations for different inverter types)
3. **Unified Response Pattern**: All REST endpoints return `JsonResult<T>` wrapper for consistent API responses
4. **Domain Model Pattern**: Clear separation between DO (Data Objects), PO (Parameter Objects), and VO (Value Objects)

### Core Components

**Controllers** (`/controller/`)
- REST API endpoints using `@RestController`
- URL patterns use kebab-case (e.g., `/api/user/login-out`)
- Authentication handled via `@NoLoginRequired` annotation for public endpoints
- Audit logging via `@OperateLogAnnotation`

**Services** (`/service/`)
- Business logic layer with `@Service` annotation
- Factory pattern for device-specific implementations
- Error handling through `BizException` for business logic errors

**Data Access** (`/mapper/`)
- MyBatis Plus for ORM
- Mappers split across datasources: `mapper/primary/`, `mapper/second/`, `mapper/third/`
- XML mapper files in `resources/mapper/`

**Domain Objects** (`/domain/`)
- **DO** (Data Objects): Database entities
- **PO** (Parameter Objects): Request DTOs with validation annotations
- **VO** (Value Objects): Response DTOs

**Configuration** (`/config/`)
- Database configurations for multiple datasources
- Security and authentication configuration
- Scheduled job configuration

## Development Conventions

### Dependency Injection
- Use `@Resource` annotation for field injection (not constructor injection)
- Example: `@Resource private UserService userService;`

### Error Handling
- Throw `BizException` for business logic errors
- Use `@Slf4j` annotation for logging
- Log errors with `log.error()` including stack traces

### Request/Response Handling
- All request bodies use `@Valid` annotation
- PO classes contain validation annotations
- All API responses wrapped in `JsonResult<T>`

### Database Operations
- Use MyBatis Plus service methods for CRUD operations
- Complex queries use mapper XML files
- Transactions handled via `@Transactional` annotation

## Project Features

The FMS Server manages:
- User authentication and authorization
- Device/Inverter management (multiple types: GoodWe, Growatt, etc.)
- Firmware management and OTA updates
- Energy monitoring and forecasting
- Site and plant management
- Telemetry data collection
- Email notifications
- Scheduled jobs for data processing

## Important Notes

- Active Spring profile is typically set to `test` (see application.yaml)
- Database connections use Druid connection pool
- The project supports multiple inverter manufacturers with device-specific implementations
- Scheduled jobs handle forecasting, monitoring, and data aggregation tasks